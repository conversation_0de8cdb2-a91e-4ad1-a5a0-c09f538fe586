"""Test script for the multi-agent search system."""

import sys
import os
import logging
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.workflow import workflow
from src.config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_basic_search():
    """Test basic search functionality."""
    print("🔍 Testing basic search functionality...")
    
    try:
        # Test query
        test_query = "What is machine learning?"
        
        print(f"Query: {test_query}")
        print("Starting multi-agent search...")
        
        # Run the workflow
        result = workflow.run(
            user_query=test_query,
            user_intent="Provide a comprehensive overview of machine learning",
            max_rounds=2
        )
        
        # Check results
        if result.get("error_message"):
            print(f"❌ Error: {result['error_message']}")
            return False
        
        final_result = result.get("final_result")
        if not final_result:
            print("❌ No final result generated")
            return False
        
        print("✅ Search completed successfully!")
        print(f"📊 Summary length: {len(final_result.summary)} characters")
        print(f"🔑 Key points: {len(final_result.key_points)}")
        print(f"📚 Sources: {len(final_result.sources)}")
        print(f"🎯 Confidence: {final_result.confidence_score:.2f}")
        print(f"🔄 Search rounds: {result.get('search_round', 1)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Basic search test failed: {e}")
        return False


def test_configuration():
    """Test system configuration."""
    print("⚙️ Testing system configuration...")
    
    try:
        # Check API keys
        has_openai = bool(Config.OPENAI_API_KEY)
        has_anthropic = bool(Config.ANTHROPIC_API_KEY)
        
        print(f"OpenAI API Key: {'✅ Configured' if has_openai else '❌ Missing'}")
        print(f"Anthropic API Key: {'✅ Configured' if has_anthropic else '❌ Missing'}")
        
        if not (has_openai or has_anthropic):
            print("❌ No API keys configured!")
            return False
        
        print(f"Default Provider: {Config.DEFAULT_LLM_PROVIDER}")
        print(f"Max Search Results: {Config.MAX_SEARCH_RESULTS}")
        print(f"Search Timeout: {Config.SEARCH_TIMEOUT}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_agents_individually():
    """Test individual agents."""
    print("🤖 Testing individual agents...")
    
    try:
        from src.agents.agent1_query_generator import QueryGeneratorAgent
        from src.agents.agent2_search_executor import SearchExecutorAgent
        from src.agents.agent3_evaluator import ResultEvaluatorAgent
        from src.agents.agent4_integrator import InformationIntegratorAgent
        from src.state import SearchQuery, SearchResult, EvaluationResult
        
        # Test Agent 1
        print("Testing Agent 1 (Query Generator)...")
        agent1 = QueryGeneratorAgent()
        queries = agent1.generate_search_queries(
            "What is Python programming?",
            "Learn about Python programming language"
        )
        print(f"✅ Generated {len(queries)} search queries")
        
        # Test Agent 2
        print("Testing Agent 2 (Search Executor)...")
        agent2 = SearchExecutorAgent()
        if queries:
            results = agent2.execute_searches(queries[:1])  # Test with one query
            print(f"✅ Found {len(results)} search results")
        else:
            print("⚠️ No queries to test search executor")
            results = []
        
        # Test Agent 3
        print("Testing Agent 3 (Result Evaluator)...")
        agent3 = ResultEvaluatorAgent()
        if results:
            evaluation = agent3.evaluate_results(
                "What is Python programming?",
                "Learn about Python programming language",
                queries[:1],
                results,
                "Basic template"
            )
            print(f"✅ Evaluation completed - Quality: {evaluation.quality_score:.2f}")
        else:
            print("⚠️ No results to evaluate")
            evaluation = EvaluationResult(
                is_sufficient=True,
                missing_aspects=[],
                quality_score=0.5,
                feedback="Test evaluation"
            )
        
        # Test Agent 4
        print("Testing Agent 4 (Information Integrator)...")
        agent4 = InformationIntegratorAgent()
        if results:
            integrated = agent4.integrate_results(
                "What is Python programming?",
                "Learn about Python programming language",
                results,
                "Basic template",
                evaluation
            )
            print(f"✅ Integration completed - Confidence: {integrated.confidence_score:.2f}")
        else:
            print("⚠️ No results to integrate")
        
        return True
        
    except Exception as e:
        print(f"❌ Individual agent test failed: {e}")
        logger.error(f"Individual agent test failed: {e}")
        return False


def run_comprehensive_test():
    """Run comprehensive system test."""
    print("🚀 Running comprehensive system test...")
    print("=" * 50)
    
    # Test configuration
    config_ok = test_configuration()
    print()
    
    # Test individual agents
    agents_ok = test_agents_individually()
    print()
    
    # Test basic search
    search_ok = test_basic_search()
    print()
    
    # Summary
    print("=" * 50)
    print("📊 Test Summary:")
    print(f"Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"Individual Agents: {'✅ PASS' if agents_ok else '❌ FAIL'}")
    print(f"Basic Search: {'✅ PASS' if search_ok else '❌ FAIL'}")
    
    overall_success = config_ok and agents_ok and search_ok
    print(f"Overall: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    return overall_success


if __name__ == "__main__":
    print("🧪 Multi-Agent Search System Test Suite")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = run_comprehensive_test()
    
    print()
    print(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("🎉 All tests passed! The system is ready to use.")
        print("💡 Run 'streamlit run app.py' to start the web interface.")
    else:
        print("⚠️ Some tests failed. Please check the configuration and try again.")
    
    sys.exit(0 if success else 1)

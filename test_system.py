"""Test script for the multi-agent search system."""

import sys
import os
import logging
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.workflow import workflow
from src.config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_basic_search():
    """Test basic search functionality."""
    print("🔍 测试基本搜索功能...")

    try:
        # Test query
        test_query = "什么是机器学习?"

        print(f"查询: {test_query}")
        print("开始多智能体搜索...")

        # Run the workflow
        result = workflow.run(
            user_query=test_query,
            user_intent="提供机器学习的全面概述",
            max_rounds=2
        )

        # Check results
        if result.get("error_message"):
            print(f"❌ 错误: {result['error_message']}")
            return False

        final_result = result.get("final_result")
        if not final_result:
            print("❌ 未生成最终结果")
            return False

        print("✅ 搜索成功完成!")
        print(f"📊 摘要长度: {len(final_result.summary)} 字符")
        print(f"🔑 关键要点: {len(final_result.key_points)}")
        print(f"📚 信息来源: {len(final_result.sources)}")
        print(f"🎯 置信度: {final_result.confidence_score:.2f}")
        print(f"🔄 搜索轮数: {result.get('search_round', 1)}")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.error(f"Basic search test failed: {e}")
        return False


def test_configuration():
    """Test system configuration."""
    print("⚙️ 测试系统配置...")

    try:
        # Check API keys and configuration
        has_openai = bool(Config.OPENAI_API_KEY)
        has_anthropic = bool(Config.ANTHROPIC_API_KEY)
        has_modelscope = bool(Config.OPENAI_API_KEY and Config.DEFAULT_LLM_PROVIDER == "modelscope")

        print(f"OpenAI API密钥: {'✅ 已配置' if has_openai else '❌ 缺失'}")
        print(f"Anthropic API密钥: {'✅ 已配置' if has_anthropic else '❌ 缺失'}")
        print(f"ModelScope配置: {'✅ 已配置' if has_modelscope else '❌ 缺失'}")

        if not (has_openai or has_anthropic or has_modelscope):
            print("❌ 未配置API密钥!")
            return False

        print(f"默认提供商: {Config.DEFAULT_LLM_PROVIDER}")
        if Config.DEFAULT_LLM_PROVIDER == "modelscope":
            print(f"模型名称: {Config.MODEL_NAME}")
            print(f"API地址: {Config.MODEL_BASE_URL}")
        print(f"最大搜索结果数: {Config.MAX_SEARCH_RESULTS}")
        print(f"搜索超时时间: {Config.SEARCH_TIMEOUT}秒")

        return True

    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_agents_individually():
    """Test individual agents."""
    print("🤖 测试各个智能体...")

    try:
        from src.agents.agent1_query_generator import QueryGeneratorAgent
        from src.agents.agent2_search_executor import SearchExecutorAgent
        from src.agents.agent3_evaluator import ResultEvaluatorAgent
        from src.agents.agent4_integrator import InformationIntegratorAgent
        from src.state import SearchQuery, SearchResult, EvaluationResult

        # Test Agent 1
        print("测试智能体1 (查询生成器)...")
        agent1 = QueryGeneratorAgent()
        queries = agent1.generate_search_queries(
            "什么是Python编程?",
            "学习Python编程语言"
        )
        print(f"✅ 生成了 {len(queries)} 个搜索查询")

        # Test Agent 2
        print("测试智能体2 (搜索执行器)...")
        agent2 = SearchExecutorAgent()
        if queries:
            results = agent2.execute_searches(queries[:1])  # Test with one query
            print(f"✅ 找到了 {len(results)} 个搜索结果")
        else:
            print("⚠️ 没有查询可供测试搜索执行器")
            results = []

        # Test Agent 3
        print("测试智能体3 (结果评估器)...")
        agent3 = ResultEvaluatorAgent()
        if results:
            evaluation = agent3.evaluate_results(
                "什么是Python编程?",
                "学习Python编程语言",
                queries[:1],
                results,
                "基本模板"
            )
            print(f"✅ 评估完成 - 质量: {evaluation.quality_score:.2f}")
        else:
            print("⚠️ 没有结果可供评估")
            evaluation = EvaluationResult(
                is_sufficient=True,
                missing_aspects=[],
                quality_score=0.5,
                feedback="测试评估"
            )

        # Test Agent 4
        print("测试智能体4 (信息整合器)...")
        agent4 = InformationIntegratorAgent()
        if results:
            integrated = agent4.integrate_results(
                "什么是Python编程?",
                "学习Python编程语言",
                results,
                "基本模板",
                evaluation
            )
            print(f"✅ 整合完成 - 置信度: {integrated.confidence_score:.2f}")
        else:
            print("⚠️ 没有结果可供整合")

        return True

    except Exception as e:
        print(f"❌ 单个智能体测试失败: {e}")
        logger.error(f"Individual agent test failed: {e}")
        return False


def run_comprehensive_test():
    """Run comprehensive system test."""
    print("🚀 运行综合系统测试...")
    print("=" * 50)

    # Test configuration
    config_ok = test_configuration()
    print()

    # Test individual agents
    agents_ok = test_agents_individually()
    print()

    # Test basic search
    search_ok = test_basic_search()
    print()

    # Summary
    print("=" * 50)
    print("📊 测试总结:")
    print(f"配置测试: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"单个智能体: {'✅ 通过' if agents_ok else '❌ 失败'}")
    print(f"基本搜索: {'✅ 通过' if search_ok else '❌ 失败'}")

    overall_success = config_ok and agents_ok and search_ok
    print(f"总体结果: {'✅ 所有测试通过' if overall_success else '❌ 部分测试失败'}")

    return overall_success


if __name__ == "__main__":
    print("🧪 多智能体搜索系统测试套件")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    success = run_comprehensive_test()

    print()
    print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    if success:
        print("🎉 所有测试通过! 系统已准备就绪。")
        print("💡 运行 'streamlit run app.py' 启动Web界面。")
    else:
        print("⚠️ 部分测试失败。请检查配置后重试。")

    sys.exit(0 if success else 1)

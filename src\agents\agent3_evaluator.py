"""Agent 3: Result Evaluator - Evaluates search results quality and completeness."""

import json
from typing import List
from langgraph.types import Command

from ..state import MultiAgentState, SearchResult, SearchQuery, EvaluationResult
from ..llm import llm_manager


class ResultEvaluatorAgent:
    """Agent responsible for evaluating search results quality and completeness."""
    
    def __init__(self):
        self.name = "result_evaluator"
    
    def evaluate_results(
        self, 
        user_query: str, 
        user_intent: str,
        search_queries: List[SearchQuery],
        search_results: List[SearchResult],
        result_template: str
    ) -> EvaluationResult:
        """Evaluate the quality and completeness of search results."""
        
        system_prompt = """You are an expert information quality evaluator. Your task is to assess whether the search results adequately address the user's query and intent.

Evaluation Criteria:
1. Completeness: Do the results cover all important aspects of the user's query?
2. Relevance: Are the results directly related to what the user is asking?
3. Quality: Are the sources credible and informative?
4. Coverage: Do the results address the different search queries that were generated?

You must return a JSON object with this exact structure:
{
  "is_sufficient": boolean,
  "missing_aspects": ["aspect1", "aspect2", ...],
  "quality_score": 0.0-1.0,
  "feedback": "detailed explanation of the evaluation"
}

Guidelines:
- is_sufficient: true if results adequately address the user's needs, false if more searching is needed
- missing_aspects: list specific topics/aspects that are missing or inadequately covered
- quality_score: overall quality rating from 0.0 (poor) to 1.0 (excellent)
- feedback: detailed explanation of strengths and weaknesses"""

        # Prepare search results summary
        results_summary = []
        for i, result in enumerate(search_results[:10], 1):  # Limit to top 10 for evaluation
            results_summary.append(f"{i}. {result.title}\n   URL: {result.url}\n   Snippet: {result.snippet[:200]}...")
        
        results_text = "\n\n".join(results_summary) if results_summary else "No search results found."
        
        # Prepare search queries summary
        queries_text = "\n".join([f"- {q.query} (Priority: {q.priority}) - {q.context}" for q in search_queries])
        
        user_prompt = f"""User Query: {user_query}
User Intent: {user_intent}

Search Queries Generated:
{queries_text}

Expected Result Template:
{result_template}

Search Results Found ({len(search_results)} total):
{results_text}

Evaluate whether these search results are sufficient to provide a comprehensive answer to the user's query."""

        messages = llm_manager.create_messages(system_prompt, user_prompt)
        response = llm_manager.invoke(messages)
        
        try:
            # Parse JSON response
            evaluation_data = json.loads(response)
            
            evaluation = EvaluationResult(
                is_sufficient=evaluation_data["is_sufficient"],
                missing_aspects=evaluation_data.get("missing_aspects", []),
                quality_score=evaluation_data.get("quality_score", 0.5),
                feedback=evaluation_data.get("feedback", "No feedback provided")
            )
            
            return evaluation
            
        except (json.JSONDecodeError, KeyError) as e:
            # Fallback evaluation
            return EvaluationResult(
                is_sufficient=len(search_results) >= 3,  # Simple heuristic
                missing_aspects=["Unable to parse detailed evaluation"],
                quality_score=0.5,
                feedback=f"Evaluation parsing failed: {str(e)}. Found {len(search_results)} results."
            )
    
    def generate_additional_queries(
        self, 
        user_query: str,
        missing_aspects: List[str],
        existing_queries: List[SearchQuery]
    ) -> List[SearchQuery]:
        """Generate additional search queries for missing aspects."""
        
        if not missing_aspects:
            return []
        
        system_prompt = """You are a search query specialist. Generate targeted search queries to address the missing aspects identified in the evaluation.

Return a JSON array of query objects with this structure:
{
  "query": "specific search query",
  "context": "explanation of what this query addresses",
  "priority": 1-5
}

Make the queries specific and targeted to fill the information gaps."""

        existing_queries_text = "\n".join([f"- {q.query}" for q in existing_queries])
        missing_aspects_text = "\n".join([f"- {aspect}" for aspect in missing_aspects])
        
        user_prompt = f"""Original User Query: {user_query}

Existing Search Queries:
{existing_queries_text}

Missing Aspects to Address:
{missing_aspects_text}

Generate 2-3 additional search queries to address these missing aspects."""

        messages = llm_manager.create_messages(system_prompt, user_prompt)
        response = llm_manager.invoke(messages)
        
        try:
            queries_data = json.loads(response)
            additional_queries = []
            
            for query_data in queries_data:
                query = SearchQuery(
                    query=query_data["query"],
                    context=query_data["context"],
                    priority=query_data.get("priority", 3)
                )
                additional_queries.append(query)
            
            return additional_queries
            
        except (json.JSONDecodeError, KeyError):
            # Fallback: create simple queries from missing aspects
            return [
                SearchQuery(
                    query=f"{user_query} {aspect}",
                    context=f"Additional search for: {aspect}",
                    priority=3
                )
                for aspect in missing_aspects[:2]  # Limit to 2 additional queries
            ]


def agent3_node(state: MultiAgentState) -> Command:
    """Agent 3 node function for LangGraph."""
    
    agent = ResultEvaluatorAgent()
    
    # Get data from state
    user_query = state.get("user_query", "")
    user_intent = state.get("user_intent", "")
    search_queries = state.get("search_queries", [])
    search_results = state.get("search_results", [])
    result_template = state.get("result_template", "")
    search_round = state.get("search_round", 1)
    max_rounds = state.get("max_rounds", 3)
    
    # Evaluate results
    evaluation = agent.evaluate_results(
        user_query, user_intent, search_queries, search_results, result_template
    )
    
    # Determine next action
    if evaluation.is_sufficient or search_round >= max_rounds:
        # Results are sufficient or max rounds reached, proceed to integration
        return Command(
            goto="agent4_integrator",
            update={
                **state,
                "evaluation": evaluation,
                "needs_more_search": False,
                "current_agent": "integrator"
            }
        )
    else:
        # Need more search rounds
        additional_queries = agent.generate_additional_queries(
            user_query, evaluation.missing_aspects, search_queries
        )
        
        # Add additional queries to existing ones
        updated_queries = search_queries + additional_queries
        
        return Command(
            goto="agent2_search",
            update={
                **state,
                "evaluation": evaluation,
                "needs_more_search": True,
                "search_queries": updated_queries,
                "search_round": search_round + 1,
                "current_agent": "search_executor"
            }
        )

"""State definitions for the multi-agent search system."""

from typing import List, Dict, Any, Optional, TypedDict
from pydantic import BaseModel, Field


class SearchQuery(BaseModel):
    """Search query model."""
    query: str = Field(description="The search query")
    context: str = Field(description="Additional context for the search")
    priority: int = Field(default=1, description="Priority level (1-5)")


class SearchResult(BaseModel):
    """Search result model."""
    title: str = Field(description="Title of the search result")
    url: str = Field(description="URL of the search result")
    snippet: str = Field(description="Snippet/summary of the content")
    relevance_score: float = Field(default=0.0, description="Relevance score (0-1)")


class EvaluationResult(BaseModel):
    """Evaluation result model."""
    is_sufficient: bool = Field(description="Whether the search results are sufficient")
    missing_aspects: List[str] = Field(default_factory=list, description="Missing aspects to search for")
    quality_score: float = Field(default=0.0, description="Overall quality score (0-1)")
    feedback: str = Field(description="Detailed feedback on the results")


class IntegratedResult(BaseModel):
    """Final integrated result model."""
    summary: str = Field(description="Comprehensive summary of all findings")
    key_points: List[str] = Field(description="Key points extracted from all sources")
    sources: List[str] = Field(description="List of source URLs")
    confidence_score: float = Field(default=0.0, description="Confidence in the result (0-1)")


class MultiAgentState(TypedDict):
    """State for the multi-agent search system."""
    
    # User input
    user_query: str
    user_intent: str
    
    # Agent 1 outputs
    search_queries: List[SearchQuery]
    result_template: str
    
    # Agent 2 outputs
    search_results: List[SearchResult]
    search_round: int
    
    # Agent 3 outputs
    evaluation: Optional[EvaluationResult]
    needs_more_search: bool
    
    # Agent 4 outputs
    final_result: Optional[IntegratedResult]
    
    # System state
    current_agent: str
    max_rounds: int
    error_message: Optional[str]
    
    # Metadata
    start_time: Optional[str]
    end_time: Optional[str]
    total_sources_found: int

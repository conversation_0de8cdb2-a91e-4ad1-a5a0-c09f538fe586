"""Command-line interface for the multi-agent search system."""

import sys
import os
import argparse
import json
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.workflow import workflow
from src.config import Config


def print_banner():
    """Print application banner."""
    print("🔍 多智能体搜索与信息整合系统")
    print("=" * 60)
    print("命令行模式 - 适合自动化和脚本使用")
    print(f"使用 {Config.MODEL_PROVIDER} - {Config.MODEL_NAME}")
    print()


def print_search_progress(state_update):
    """Print search progress updates."""
    current_agent = state_update.get("current_agent", "unknown")
    search_round = state_update.get("search_round", 1)
    
    agent_names = {
        "query_generator": "🎯 Query Generator",
        "search_executor": "🔍 Search Executor", 
        "evaluator": "📊 Result Evaluator",
        "integrator": "🔗 Information Integrator"
    }
    
    agent_display = agent_names.get(current_agent, current_agent.replace("_", " ").title())
    
    if search_round > 1:
        print(f"[Round {search_round}] {agent_display} working...")
    else:
        print(f"{agent_display} working...")


def format_results(final_state):
    """Format and display search results."""
    if final_state.get("error_message"):
        print(f"❌ 搜索失败: {final_state['error_message']}")
        return

    final_result = final_state.get("final_result")
    if not final_result:
        print("⚠️ 未生成结果")
        return

    print("\n" + "=" * 60)
    print("📋 搜索结果")
    print("=" * 60)

    # Summary
    print("\n📝 摘要:")
    print("-" * 40)
    print(final_result.summary)

    # Key points
    if final_result.key_points:
        print("\n🔑 关键要点:")
        print("-" * 40)
        for i, point in enumerate(final_result.key_points, 1):
            print(f"{i}. {point}")

    # Sources
    if final_result.sources:
        print("\n📚 信息来源:")
        print("-" * 40)
        for i, source in enumerate(final_result.sources, 1):
            print(f"{i}. {source}")

    # Metadata
    print("\n📊 元数据:")
    print("-" * 40)
    print(f"置信度评分: {final_result.confidence_score:.2f}")
    print(f"找到的来源总数: {final_state.get('total_sources_found', 0)}")
    print(f"搜索轮数: {final_state.get('search_round', 1)}")

    # Timing
    start_time = final_state.get("start_time")
    end_time = final_state.get("end_time")
    if start_time and end_time:
        start_dt = datetime.fromisoformat(start_time)
        end_dt = datetime.fromisoformat(end_time)
        duration = (end_dt - start_dt).total_seconds()
        print(f"搜索耗时: {duration:.2f} 秒")


def interactive_mode():
    """Run in interactive mode."""
    print_banner()
    print("💬 交互模式 - 输入 'quit' 退出")
    print()

    while True:
        try:
            # Get user input
            query = input("🔍 输入您的搜索查询: ").strip()

            if query.lower() in ['quit', 'exit', 'q', '退出', '结束']:
                print("👋 再见!")
                break

            if not query:
                print("⚠️ 请输入搜索查询")
                continue

            # Optional intent
            intent = input("🎯 指定您的意图（可选，按回车跳过）: ").strip()
            if not intent:
                intent = None

            # Run search
            print("\n🚀 开始多智能体搜索...")
            print("-" * 40)
            
            final_state = workflow.run(
                user_query=query,
                user_intent=intent,
                max_rounds=3
            )
            
            # Display results
            format_results(final_state)
            print("\n" + "=" * 60)
            print()
            
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
            print()


def batch_mode(query, intent=None, output_file=None, max_rounds=3):
    """Run in batch mode."""
    print_banner()
    print(f"📝 批处理模式 - 查询: {query}")
    if intent:
        print(f"🎯 意图: {intent}")
    print()

    try:
        # Run search
        print("🚀 开始多智能体搜索...")

        final_state = workflow.run(
            user_query=query,
            user_intent=intent,
            max_rounds=max_rounds
        )

        # Display results
        format_results(final_state)

        # Save to file if requested
        if output_file:
            output_data = {
                "query": query,
                "intent": intent,
                "timestamp": datetime.now().isoformat(),
                "results": {
                    "summary": final_state.get("final_result", {}).get("summary", ""),
                    "key_points": final_state.get("final_result", {}).get("key_points", []),
                    "sources": final_state.get("final_result", {}).get("sources", []),
                    "confidence_score": final_state.get("final_result", {}).get("confidence_score", 0.0),
                    "metadata": {
                        "total_sources": final_state.get("total_sources_found", 0),
                        "search_rounds": final_state.get("search_round", 1),
                        "duration": final_state.get("end_time", "") and final_state.get("start_time", "") and
                                  (datetime.fromisoformat(final_state["end_time"]) -
                                   datetime.fromisoformat(final_state["start_time"])).total_seconds()
                    }
                }
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)

            print(f"\n💾 结果已保存到: {output_file}")

        return 0

    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        return 1


def main():
    parser = argparse.ArgumentParser(
        description="多智能体搜索系统命令行界面",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python cli.py                                    # 交互模式
  python cli.py -q "什么是机器学习?"                  # 单次查询
  python cli.py -q "AI趋势" -i "研究论文" -o results.json  # 带意图和输出文件
        """
    )

    parser.add_argument("-q", "--query", help="搜索查询")
    parser.add_argument("-i", "--intent", help="搜索意图/目的")
    parser.add_argument("-o", "--output", help="结果输出文件 (JSON格式)")
    parser.add_argument("-r", "--rounds", type=int, default=3, help="最大搜索轮数 (默认: 3)")
    parser.add_argument("--interactive", action="store_true", help="强制交互模式")

    args = parser.parse_args()

    # Check configuration
    try:
        Config.validate()
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        print("💡 请检查您的.env文件和API密钥")
        return 1
    
    # Determine mode
    if args.query and not args.interactive:
        # Batch mode
        return batch_mode(
            query=args.query,
            intent=args.intent,
            output_file=args.output,
            max_rounds=args.rounds
        )
    else:
        # Interactive mode
        interactive_mode()
        return 0


if __name__ == "__main__":
    sys.exit(main())

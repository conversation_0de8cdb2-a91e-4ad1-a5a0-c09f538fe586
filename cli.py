"""Command-line interface for the multi-agent search system."""

import sys
import os
import argparse
import json
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.workflow import workflow
from src.config import Config


def print_banner():
    """Print application banner."""
    print("🔍 Multi-Agent Search & Information Integration System")
    print("=" * 60)
    print("CLI Mode - Perfect for automation and scripting")
    print()


def print_search_progress(state_update):
    """Print search progress updates."""
    current_agent = state_update.get("current_agent", "unknown")
    search_round = state_update.get("search_round", 1)
    
    agent_names = {
        "query_generator": "🎯 Query Generator",
        "search_executor": "🔍 Search Executor", 
        "evaluator": "📊 Result Evaluator",
        "integrator": "🔗 Information Integrator"
    }
    
    agent_display = agent_names.get(current_agent, current_agent.replace("_", " ").title())
    
    if search_round > 1:
        print(f"[Round {search_round}] {agent_display} working...")
    else:
        print(f"{agent_display} working...")


def format_results(final_state):
    """Format and display search results."""
    if final_state.get("error_message"):
        print(f"❌ Search failed: {final_state['error_message']}")
        return
    
    final_result = final_state.get("final_result")
    if not final_result:
        print("⚠️ No results generated")
        return
    
    print("\n" + "=" * 60)
    print("📋 SEARCH RESULTS")
    print("=" * 60)
    
    # Summary
    print("\n📝 SUMMARY:")
    print("-" * 40)
    print(final_result.summary)
    
    # Key points
    if final_result.key_points:
        print("\n🔑 KEY POINTS:")
        print("-" * 40)
        for i, point in enumerate(final_result.key_points, 1):
            print(f"{i}. {point}")
    
    # Sources
    if final_result.sources:
        print("\n📚 SOURCES:")
        print("-" * 40)
        for i, source in enumerate(final_result.sources, 1):
            print(f"{i}. {source}")
    
    # Metadata
    print("\n📊 METADATA:")
    print("-" * 40)
    print(f"Confidence Score: {final_result.confidence_score:.2f}")
    print(f"Total Sources Found: {final_state.get('total_sources_found', 0)}")
    print(f"Search Rounds: {final_state.get('search_round', 1)}")
    
    # Timing
    start_time = final_state.get("start_time")
    end_time = final_state.get("end_time")
    if start_time and end_time:
        start_dt = datetime.fromisoformat(start_time)
        end_dt = datetime.fromisoformat(end_time)
        duration = (end_dt - start_dt).total_seconds()
        print(f"Search Duration: {duration:.2f} seconds")


def interactive_mode():
    """Run in interactive mode."""
    print_banner()
    print("💬 Interactive Mode - Type 'quit' to exit")
    print()
    
    while True:
        try:
            # Get user input
            query = input("🔍 Enter your search query: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not query:
                print("⚠️ Please enter a search query")
                continue
            
            # Optional intent
            intent = input("🎯 Specify your intent (optional, press Enter to skip): ").strip()
            if not intent:
                intent = None
            
            # Run search
            print("\n🚀 Starting multi-agent search...")
            print("-" * 40)
            
            final_state = workflow.run(
                user_query=query,
                user_intent=intent,
                max_rounds=3
            )
            
            # Display results
            format_results(final_state)
            print("\n" + "=" * 60)
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            print()


def batch_mode(query, intent=None, output_file=None, max_rounds=3):
    """Run in batch mode."""
    print_banner()
    print(f"📝 Batch Mode - Query: {query}")
    if intent:
        print(f"🎯 Intent: {intent}")
    print()
    
    try:
        # Run search
        print("🚀 Starting multi-agent search...")
        
        final_state = workflow.run(
            user_query=query,
            user_intent=intent,
            max_rounds=max_rounds
        )
        
        # Display results
        format_results(final_state)
        
        # Save to file if requested
        if output_file:
            output_data = {
                "query": query,
                "intent": intent,
                "timestamp": datetime.now().isoformat(),
                "results": {
                    "summary": final_state.get("final_result", {}).get("summary", ""),
                    "key_points": final_state.get("final_result", {}).get("key_points", []),
                    "sources": final_state.get("final_result", {}).get("sources", []),
                    "confidence_score": final_state.get("final_result", {}).get("confidence_score", 0.0),
                    "metadata": {
                        "total_sources": final_state.get("total_sources_found", 0),
                        "search_rounds": final_state.get("search_round", 1),
                        "duration": final_state.get("end_time", "") and final_state.get("start_time", "") and 
                                  (datetime.fromisoformat(final_state["end_time"]) - 
                                   datetime.fromisoformat(final_state["start_time"])).total_seconds()
                    }
                }
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Results saved to: {output_file}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Search failed: {e}")
        return 1


def main():
    parser = argparse.ArgumentParser(
        description="Multi-Agent Search System CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python cli.py                                    # Interactive mode
  python cli.py -q "What is machine learning?"     # Single query
  python cli.py -q "AI trends" -i "Research paper" -o results.json  # With intent and output file
        """
    )
    
    parser.add_argument("-q", "--query", help="Search query")
    parser.add_argument("-i", "--intent", help="Search intent/purpose")
    parser.add_argument("-o", "--output", help="Output file for results (JSON format)")
    parser.add_argument("-r", "--rounds", type=int, default=3, help="Maximum search rounds (default: 3)")
    parser.add_argument("--interactive", action="store_true", help="Force interactive mode")
    
    args = parser.parse_args()
    
    # Check configuration
    try:
        Config.validate()
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("💡 Please check your .env file and API keys")
        return 1
    
    # Determine mode
    if args.query and not args.interactive:
        # Batch mode
        return batch_mode(
            query=args.query,
            intent=args.intent,
            output_file=args.output,
            max_rounds=args.rounds
        )
    else:
        # Interactive mode
        interactive_mode()
        return 0


if __name__ == "__main__":
    sys.exit(main())

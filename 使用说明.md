# 🔍 多智能体搜索与信息整合系统 - 使用说明

## 📋 系统概述

本系统是一个基于LangGraph和Streamlit的智能搜索系统，使用四个专门的AI智能体协作，为用户查询提供全面、深入的搜索结果。

### 🤖 四个智能体介绍

1. **智能体1 - 查询生成器**: 分析用户意图，生成多个有针对性的搜索查询和结果模板
2. **智能体2 - 搜索执行器**: 使用Tavily执行高质量网络搜索，收集和排序结果
3. **智能体3 - 结果评估器**: 评估搜索结果质量，决定是否需要更多搜索
4. **智能体4 - 信息整合器**: 整合所有发现，生成综合性答案

## 🚀 快速开始

### 1. 环境配置

确保您已经配置了ModelScope API密钥：

```env
# .env 文件配置
OPENAI_API_KEY=您的ModelScope_API密钥
MODEL_BASE_URL=https://api-inference.modelscope.cn/v1/
MODEL_NAME=Qwen/Qwen3-Coder-480B-A35B-Instruct
MODEL_PROVIDER=ModelScope
DEFAULT_LLM_PROVIDER=modelscope

# Tavily搜索API配置
TAVILY_API_KEY=您的Tavily_API密钥
```

### 2. 启动方式

#### 方式一：Web界面（推荐）
```bash
streamlit run app.py
```
然后在浏览器中访问 `http://localhost:8501`

#### 方式二：命令行界面
```bash
# 交互模式
python cli.py

# 单次查询
python cli.py -q "您的问题"

# 保存结果到文件
python cli.py -q "您的问题" -o results.json
```

#### 方式三：一键启动
```bash
python run.py  # 自动测试后启动Web界面
```

## 🎯 使用指南

### Web界面使用

1. **输入查询**: 在搜索框中输入您的问题
   - 例如："人工智能的最新发展是什么？"
   - 例如："Python编程的最佳实践有哪些？"

2. **设置意图**（可选）: 在高级选项中指定您的具体需求
   - 例如："我需要为研究论文准备资料"
   - 例如："我想了解技术实现细节"

3. **配置选项**: 在侧边栏调整设置
   - LLM提供商选择
   - 最大搜索轮数（1-5轮）

4. **观察进度**: 实时查看四个智能体的工作状态
   - 🔄 表示正在工作
   - ✅ 表示已完成
   - ⏳ 表示等待中

5. **查看结果**: 获得结构化的搜索结果
   - 📝 摘要：综合性总结
   - 🔑 关键要点：重要发现列表
   - 📚 信息来源：参考链接
   - 📊 元数据：置信度、来源数量等

### 命令行使用

#### 交互模式
```bash
python cli.py
# 然后按提示输入查询和意图
```

#### 批处理模式
```bash
# 基本查询
python cli.py -q "什么是深度学习？"

# 带意图的查询
python cli.py -q "机器学习算法" -i "技术对比分析"

# 保存结果
python cli.py -q "AI发展趋势" -o results.json

# 设置搜索轮数
python cli.py -q "您的问题" -r 5
```

## 🔧 高级功能

### 多轮搜索
- 系统会自动评估初始搜索结果的质量
- 如果信息不够充分，会自动进行额外搜索轮次
- 最多支持5轮搜索，确保结果的全面性

### 智能评估
- AI驱动的结果质量评估
- 自动识别缺失的信息方面
- 提供详细的反馈和改进建议

### 信息整合
- 将多个来源的信息综合成连贯的答案
- 提取关键见解和要点
- 提供置信度评分

### 搜索历史
- Web界面自动保存搜索历史
- 可以回顾之前的搜索结果
- 支持历史记录的快速访问

## 📊 结果解读

### 摘要部分
- 提供查询主题的全面概述
- 整合多个来源的信息
- 结构化呈现关键内容

### 关键要点
- 列出最重要的发现
- 突出核心概念和见解
- 便于快速理解主要内容

### 信息来源
- 提供所有参考链接
- 支持点击访问原始资料
- 确保信息的可追溯性

### 置信度评分
- 0.0-1.0的评分范围
- 反映结果的可靠性
- 帮助判断信息质量

## 🛠️ 故障排除

### 常见问题

1. **API密钥错误**
   - 检查.env文件中的OPENAI_API_KEY设置
   - 确保使用有效的ModelScope API密钥

2. **搜索失败**
   - 检查网络连接
   - 确认搜索超时设置合理

3. **响应缓慢**
   - ModelScope模型较大，首次调用需要时间
   - 可以减少最大搜索轮数

4. **结果质量不佳**
   - 尝试更具体的查询
   - 在意图中提供更多上下文

### 测试系统
```bash
# 测试ModelScope配置
python test_modelscope.py

# 完整系统测试
python test_system.py
```

## 💡 使用技巧

1. **查询优化**
   - 使用具体、明确的问题
   - 避免过于宽泛的查询
   - 可以使用中文或英文提问

2. **意图设置**
   - 明确说明您的使用目的
   - 指定所需的详细程度
   - 提及特定的关注点

3. **结果利用**
   - 查看详细的搜索过程
   - 利用提供的源链接深入了解
   - 保存重要的搜索结果

## 🎉 开始使用

现在您已经了解了系统的基本使用方法，可以开始您的智能搜索之旅了！

```bash
# 快速启动
streamlit run app.py
```

祝您使用愉快！ 🚀

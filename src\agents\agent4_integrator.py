"""Agent 4: Information Integrator - Synthesizes search results into comprehensive answers."""

import json
from typing import List
from datetime import datetime
from langgraph.types import Command

from ..state import MultiAgentState, SearchResult, IntegratedResult, EvaluationResult
from ..llm import llm_manager


class InformationIntegratorAgent:
    """Agent responsible for integrating search results into comprehensive answers."""
    
    def __init__(self):
        self.name = "information_integrator"
    
    def integrate_results(
        self,
        user_query: str,
        user_intent: str,
        search_results: List[SearchResult],
        result_template: str,
        evaluation: EvaluationResult
    ) -> IntegratedResult:
        """Integrate search results into a comprehensive answer."""
        
        system_prompt = """You are an expert information synthesizer. Your task is to create a comprehensive, well-structured answer by integrating information from multiple search results.

Guidelines:
1. Create a coherent narrative that addresses the user's query completely
2. Organize information logically following the provided template structure
3. Extract key insights and present them clearly
4. Include relevant details while maintaining readability
5. Cite sources appropriately
6. Provide balanced coverage of different perspectives when applicable

Return a JSON object with this structure:
{
  "summary": "comprehensive summary addressing the user's query",
  "key_points": ["point1", "point2", "point3", ...],
  "sources": ["url1", "url2", "url3", ...],
  "confidence_score": 0.0-1.0
}

The summary should be detailed and informative, the key_points should highlight the most important findings, sources should list the URLs used, and confidence_score should reflect how well the results address the query."""

        # Prepare search results for integration
        results_content = []
        source_urls = []
        
        for i, result in enumerate(search_results, 1):
            results_content.append(f"""
Source {i}: {result.title}
URL: {result.url}
Content: {result.snippet}
Relevance Score: {result.relevance_score:.2f}
""")
            source_urls.append(result.url)
        
        results_text = "\n".join(results_content) if results_content else "No search results available."
        
        user_prompt = f"""User Query: {user_query}
User Intent: {user_intent}

Result Template to Follow:
{result_template}

Evaluation Feedback:
Quality Score: {evaluation.quality_score:.2f}
Feedback: {evaluation.feedback}

Search Results to Integrate:
{results_text}

Create a comprehensive, well-structured answer that integrates all relevant information from the search results."""

        messages = llm_manager.create_messages(system_prompt, user_prompt)
        response = llm_manager.invoke(messages)
        
        try:
            # Parse JSON response
            integration_data = json.loads(response)
            
            integrated_result = IntegratedResult(
                summary=integration_data.get("summary", "No summary available"),
                key_points=integration_data.get("key_points", []),
                sources=integration_data.get("sources", source_urls),
                confidence_score=integration_data.get("confidence_score", evaluation.quality_score)
            )
            
            return integrated_result
            
        except (json.JSONDecodeError, KeyError) as e:
            # Fallback integration
            fallback_summary = self._create_fallback_summary(user_query, search_results)
            
            return IntegratedResult(
                summary=fallback_summary,
                key_points=[f"Found {len(search_results)} relevant sources"],
                sources=source_urls,
                confidence_score=evaluation.quality_score
            )
    
    def _create_fallback_summary(self, user_query: str, search_results: List[SearchResult]) -> str:
        """Create a basic summary when JSON parsing fails."""
        if not search_results:
            return f"No search results were found for the query: {user_query}"
        
        summary_parts = [f"Based on {len(search_results)} search results for '{user_query}':"]
        
        for i, result in enumerate(search_results[:5], 1):  # Top 5 results
            summary_parts.append(f"{i}. {result.title}: {result.snippet[:150]}...")
        
        return "\n\n".join(summary_parts)
    
    def enhance_with_cross_references(self, integrated_result: IntegratedResult) -> IntegratedResult:
        """Enhance the integrated result with cross-references and additional insights."""
        
        system_prompt = """You are tasked with enhancing an integrated research result by adding cross-references, identifying patterns, and providing additional insights.

Review the provided summary and key points, then enhance them by:
1. Identifying connections between different sources
2. Highlighting any contradictions or different perspectives
3. Adding context or background information
4. Suggesting areas for further research if applicable

Return the enhanced content in the same JSON format."""

        user_prompt = f"""Current Summary:
{integrated_result.summary}

Current Key Points:
{chr(10).join([f"- {point}" for point in integrated_result.key_points])}

Sources Used: {len(integrated_result.sources)} sources
Confidence Score: {integrated_result.confidence_score}

Enhance this result with cross-references and additional insights."""

        messages = llm_manager.create_messages(system_prompt, user_prompt)
        response = llm_manager.invoke(messages)
        
        try:
            enhanced_data = json.loads(response)
            
            # Update the integrated result with enhanced content
            integrated_result.summary = enhanced_data.get("summary", integrated_result.summary)
            integrated_result.key_points = enhanced_data.get("key_points", integrated_result.key_points)
            
            return integrated_result
            
        except (json.JSONDecodeError, KeyError):
            # Return original if enhancement fails
            return integrated_result


def agent4_node(state: MultiAgentState) -> Command:
    """Agent 4 node function for LangGraph."""
    
    agent = InformationIntegratorAgent()
    
    # Get data from state
    user_query = state.get("user_query", "")
    user_intent = state.get("user_intent", "")
    search_results = state.get("search_results", [])
    result_template = state.get("result_template", "")
    evaluation = state.get("evaluation")
    
    # Create default evaluation if none exists
    if not evaluation:
        from ..state import EvaluationResult
        evaluation = EvaluationResult(
            is_sufficient=True,
            missing_aspects=[],
            quality_score=0.5,
            feedback="No evaluation performed"
        )
    
    # Integrate results
    integrated_result = agent.integrate_results(
        user_query, user_intent, search_results, result_template, evaluation
    )
    
    # Enhance with cross-references if we have good results
    if integrated_result.confidence_score > 0.6 and len(search_results) > 3:
        integrated_result = agent.enhance_with_cross_references(integrated_result)
    
    # Update final state
    final_state = {
        **state,
        "final_result": integrated_result,
        "current_agent": "completed",
        "end_time": datetime.now().isoformat()
    }
    
    return Command(
        goto="__end__",
        update=final_state
    )

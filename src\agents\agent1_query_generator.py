"""Agent 1: Search Query Generator - Generates search queries and result templates."""

import json
from typing import List
from langgraph.types import Command

from ..state import MultiAgentState, SearchQuery
from ..llm import llm_manager


class QueryGeneratorAgent:
    """Agent responsible for generating search queries and result templates."""
    
    def __init__(self):
        self.name = "query_generator"
    
    def generate_search_queries(self, user_query: str, user_intent: str) -> List[SearchQuery]:
        """Generate multiple search queries based on user input."""
        
        system_prompt = """You are an expert search query generator. Your task is to analyze user queries and generate multiple targeted search queries that will comprehensively address the user's information needs.

Guidelines:
1. Generate 3-5 diverse search queries that approach the topic from different angles
2. Include both broad and specific queries
3. Consider different aspects, perspectives, and related topics
4. Assign priority levels (1-5, where 5 is highest priority)
5. Provide context for each query explaining why it's important

Return your response as a JSON array of objects with this structure:
{
  "query": "search query text",
  "context": "explanation of why this query is important",
  "priority": 1-5
}"""

        user_prompt = f"""User Query: {user_query}
User Intent: {user_intent}

Generate comprehensive search queries to fully address this request."""

        messages = llm_manager.create_messages(system_prompt, user_prompt)
        response = llm_manager.invoke(messages)
        
        try:
            # Parse JSON response
            queries_data = json.loads(response)
            queries = []
            
            for query_data in queries_data:
                query = SearchQuery(
                    query=query_data["query"],
                    context=query_data["context"],
                    priority=query_data.get("priority", 3)
                )
                queries.append(query)
            
            return queries
            
        except (json.JSONDecodeError, KeyError) as e:
            # Fallback: create basic queries from the original user query
            return [
                SearchQuery(
                    query=user_query,
                    context="Primary user query",
                    priority=5
                ),
                SearchQuery(
                    query=f"{user_query} overview",
                    context="General overview of the topic",
                    priority=4
                ),
                SearchQuery(
                    query=f"{user_query} latest information",
                    context="Recent developments and updates",
                    priority=3
                )
            ]
    
    def generate_result_template(self, user_query: str, user_intent: str, queries: List[SearchQuery]) -> str:
        """Generate a template for the final result structure."""
        
        system_prompt = """You are an expert information architect. Create a comprehensive template structure for presenting search results that will best serve the user's needs.

The template should:
1. Be well-organized and logical
2. Include all important aspects the user might want to know
3. Specify what type of information should go in each section
4. Be adaptable to the search results that will be found

Return a detailed template structure with section headers and descriptions of what content should go in each section."""

        user_prompt = f"""User Query: {user_query}
User Intent: {user_intent}

Search Queries Generated:
{chr(10).join([f"- {q.query} (Priority: {q.priority})" for q in queries])}

Create an optimal result template structure for presenting the findings."""

        messages = llm_manager.create_messages(system_prompt, user_prompt)
        template = llm_manager.invoke(messages)
        
        return template


def agent1_node(state: MultiAgentState) -> Command:
    """Agent 1 node function for LangGraph."""
    
    agent = QueryGeneratorAgent()
    
    # Generate search queries
    queries = agent.generate_search_queries(
        state["user_query"], 
        state["user_intent"]
    )
    
    # Generate result template
    template = agent.generate_result_template(
        state["user_query"],
        state["user_intent"], 
        queries
    )
    
    # Update state
    updated_state = {
        **state,
        "search_queries": queries,
        "result_template": template,
        "current_agent": "search_executor",
        "search_round": 1
    }
    
    return Command(
        goto="agent2_search",
        update=updated_state
    )

"""Test script specifically for ModelScope configuration."""

import sys
import os
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import Config
from src.llm import llm_manager


def test_modelscope_config():
    """Test ModelScope configuration."""
    print("🔧 测试ModelScope配置")
    print("=" * 50)

    try:
        # Check configuration
        print(f"ModelScope API密钥: {'✅ 已设置' if Config.OPENAI_API_KEY else '❌ 缺失'}")
        print(f"API地址: {Config.MODEL_BASE_URL}")
        print(f"模型名称: {Config.MODEL_NAME}")
        print(f"提供商: {Config.MODEL_PROVIDER}")
        print(f"默认提供商: {Config.DEFAULT_LLM_PROVIDER}")
        print(f"Tavily API密钥: {'✅ 已设置' if Config.TAVILY_API_KEY else '❌ 缺失'}")

        # Validate configuration
        Config.validate()
        print("✅ 配置验证通过")

        return True

    except Exception as e:
        print(f"❌ 配置错误: {e}")
        return False


def test_modelscope_llm():
    """Test ModelScope LLM connection."""
    print("\n🤖 测试ModelScope LLM连接")
    print("=" * 50)

    try:
        # Get model instance
        model = llm_manager.get_model("modelscope")
        print("✅ 模型实例创建成功")

        # Test simple query
        print("🔍 测试简单查询...")
        messages = llm_manager.create_messages(
            "你是一个有用的助手。",
            "你好！请回复'你好，我工作正常！'来确认连接。"
        )

        response = llm_manager.invoke(messages, "modelscope")
        print(f"📝 响应: {response}")

        if "你好" in response or "正常" in response or "Hello" in response or "working" in response:
            print("✅ LLM连接测试通过")
            return True
        else:
            print("⚠️ LLM有响应但内容可能不符合预期")
            return True

    except Exception as e:
        print(f"❌ LLM连接错误: {e}")
        return False


def test_search_query_generation():
    """Test search query generation with ModelScope."""
    print("\n🎯 测试搜索查询生成")
    print("=" * 50)

    try:
        from src.agents.agent1_query_generator import QueryGeneratorAgent

        agent = QueryGeneratorAgent()
        queries = agent.generate_search_queries(
            "什么是人工智能？",
            "我需要了解人工智能的基本概念"
        )

        print(f"✅ 生成了 {len(queries)} 个搜索查询:")
        for i, query in enumerate(queries, 1):
            print(f"  {i}. {query.query} (优先级: {query.priority})")
            print(f"     上下文: {query.context}")

        return len(queries) > 0

    except Exception as e:
        print(f"❌ 查询生成错误: {e}")
        return False


def test_tavily_search():
    """Test Tavily search functionality."""
    print("\n🔍 测试Tavily搜索功能")
    print("=" * 50)

    try:
        from src.tools import search_tools

        # Test simple search
        test_query = "人工智能最新发展"
        print(f"测试查询: {test_query}")

        results = search_tools.tavily_search(test_query, max_results=3)

        if results:
            print(f"✅ 找到 {len(results)} 个搜索结果:")
            for i, result in enumerate(results, 1):
                print(f"  {i}. {result.title}")
                print(f"     URL: {result.url}")
                print(f"     相关性评分: {result.relevance_score:.2f}")
                print(f"     摘要: {result.snippet[:100]}...")
                print()
            return True
        else:
            print("⚠️ 未找到搜索结果")
            return False

    except Exception as e:
        print(f"❌ Tavily搜索错误: {e}")
        return False


def main():
    """Run ModelScope-specific tests."""
    print("🧪 ModelScope配置测试套件")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Test configuration
    config_ok = test_modelscope_config()

    # Test LLM connection
    llm_ok = test_modelscope_llm() if config_ok else False

    # Test query generation
    query_ok = test_search_query_generation() if llm_ok else False

    # Test Tavily search
    tavily_ok = test_tavily_search() if config_ok else False

    # Summary
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    print(f"配置测试: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"LLM连接: {'✅ 通过' if llm_ok else '❌ 失败'}")
    print(f"查询生成: {'✅ 通过' if query_ok else '❌ 失败'}")
    print(f"Tavily搜索: {'✅ 通过' if tavily_ok else '❌ 失败'}")

    overall_success = config_ok and llm_ok and query_ok and tavily_ok
    print(f"总体结果: {'✅ 所有测试通过' if overall_success else '❌ 部分测试失败'}")

    if overall_success:
        print("\n🎉 ModelScope配置工作正常!")
        print("💡 现在可以运行完整系统: streamlit run app.py")
    else:
        print("\n⚠️ 请检查.env文件中的ModelScope配置")
        print("💡 确保您的API密钥和模型设置正确")

    print(f"\n⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    return 0 if overall_success else 1


if __name__ == "__main__":
    sys.exit(main())

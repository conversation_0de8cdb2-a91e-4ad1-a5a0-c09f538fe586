"""Test script specifically for ModelScope configuration."""

import sys
import os
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.config import Config
from src.llm import llm_manager


def test_modelscope_config():
    """Test ModelScope configuration."""
    print("🔧 Testing ModelScope Configuration")
    print("=" * 50)
    
    try:
        # Check configuration
        print(f"API Key: {'✅ Set' if Config.OPENAI_API_KEY else '❌ Missing'}")
        print(f"Base URL: {Config.MODEL_BASE_URL}")
        print(f"Model Name: {Config.MODEL_NAME}")
        print(f"Provider: {Config.MODEL_PROVIDER}")
        print(f"Default Provider: {Config.DEFAULT_LLM_PROVIDER}")
        
        # Validate configuration
        Config.validate()
        print("✅ Configuration validation passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


def test_modelscope_llm():
    """Test ModelScope LLM connection."""
    print("\n🤖 Testing ModelScope LLM Connection")
    print("=" * 50)
    
    try:
        # Get model instance
        model = llm_manager.get_model("modelscope")
        print("✅ Model instance created successfully")
        
        # Test simple query
        print("🔍 Testing simple query...")
        messages = llm_manager.create_messages(
            "You are a helpful assistant.",
            "Hello! Please respond with 'Hello, I am working correctly!' to confirm the connection."
        )
        
        response = llm_manager.invoke(messages, "modelscope")
        print(f"📝 Response: {response}")
        
        if "Hello" in response or "working" in response:
            print("✅ LLM connection test passed")
            return True
        else:
            print("⚠️ LLM responded but content may be unexpected")
            return True
            
    except Exception as e:
        print(f"❌ LLM connection error: {e}")
        return False


def test_search_query_generation():
    """Test search query generation with ModelScope."""
    print("\n🎯 Testing Search Query Generation")
    print("=" * 50)
    
    try:
        from src.agents.agent1_query_generator import QueryGeneratorAgent
        
        agent = QueryGeneratorAgent()
        queries = agent.generate_search_queries(
            "什么是人工智能？",
            "我需要了解人工智能的基本概念"
        )
        
        print(f"✅ Generated {len(queries)} search queries:")
        for i, query in enumerate(queries, 1):
            print(f"  {i}. {query.query} (优先级: {query.priority})")
            print(f"     上下文: {query.context}")
        
        return len(queries) > 0
        
    except Exception as e:
        print(f"❌ Query generation error: {e}")
        return False


def main():
    """Run ModelScope-specific tests."""
    print("🧪 ModelScope Configuration Test Suite")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test configuration
    config_ok = test_modelscope_config()
    
    # Test LLM connection
    llm_ok = test_modelscope_llm() if config_ok else False
    
    # Test query generation
    query_ok = test_search_query_generation() if llm_ok else False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"LLM Connection: {'✅ PASS' if llm_ok else '❌ FAIL'}")
    print(f"Query Generation: {'✅ PASS' if query_ok else '❌ FAIL'}")
    
    overall_success = config_ok and llm_ok and query_ok
    print(f"Overall: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 ModelScope configuration is working correctly!")
        print("💡 You can now run the full system with: streamlit run app.py")
    else:
        print("\n⚠️ Please check your ModelScope configuration in .env file")
        print("💡 Make sure your API key and model settings are correct")
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return 0 if overall_success else 1


if __name__ == "__main__":
    sys.exit(main())

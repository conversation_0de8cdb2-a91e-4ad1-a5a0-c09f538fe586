# Multi-Agent Search & Information Integration System

A sophisticated search system powered by LangGraph and Streamlit that uses four specialized AI agents to provide comprehensive, well-researched answers to user queries.

## 🎯 System Overview

This system employs a multi-agent architecture where each agent has a specific role:

- **Agent 1 (Query Generator)**: Analyzes user intent and generates multiple targeted search queries with result templates
- **Agent 2 (Search Executor)**: Performs multi-round web searches using the generated queries
- **Agent 3 (Result Evaluator)**: Evaluates search result quality and determines if additional searches are needed
- **Agent 4 (Information Integrator)**: Synthesizes all findings into a comprehensive, well-structured answer

## 🚀 Features

- **Multi-round Search**: Automatically performs additional searches if initial results are insufficient
- **Intelligent Query Generation**: Creates diverse search queries from different angles
- **Quality Evaluation**: AI-powered assessment of result completeness and relevance
- **Information Integration**: Synthesizes multiple sources into coherent answers
- **Real-time Progress**: Live updates showing which agent is currently active
- **Search History**: Keeps track of previous searches and results
- **Flexible LLM Support**: Works with OpenAI GPT and Anthropic Claude models

## 📋 Prerequisites

- Python 3.11 or higher
- API key for at least one LLM provider (OpenAI or Anthropic)

## 🛠️ Installation

1. Install dependencies using uv (recommended) or pip:
```bash
# Using uv
uv sync

# Or using pip
pip install -e .
```

2. Configure environment variables:
```bash
cp .env.example .env
```

3. Edit `.env` file with your API keys:
```env
# Required: At least one API key
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Configuration
DEFAULT_LLM_PROVIDER=openai
OPENAI_MODEL=gpt-4o-mini
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022
```

## 🏃‍♂️ Usage

### Running the Streamlit App

```bash
streamlit run app.py
```

The application will be available at `http://localhost:8501`

### Using the System

1. **Enter your search query** in the main input field
2. **Optionally specify your intent** for more targeted results
3. **Configure settings** in the sidebar (LLM provider, max search rounds)
4. **Click "Search"** to start the multi-agent process
5. **Monitor progress** as each agent completes its task
6. **Review results** including summary, key points, and sources

### Example Queries

- "What are the latest developments in artificial intelligence?"
- "How does climate change affect ocean ecosystems?"
- "What are the best practices for microservices architecture?"
- "Compare different renewable energy technologies"

## 🏗️ Architecture

### System Flow

```
User Query → Agent 1 → Agent 2 → Agent 3 → Agent 4 → Final Result
                         ↑         ↓
                         └─────────┘
                      (Additional search rounds if needed)
```

### Project Structure

```
langgraph-augment-2/
├── app.py                          # Streamlit web interface
├── cli.py                          # Command-line interface
├── run.py                          # Simple runner script
├── test_system.py                  # System test suite
├── .env.example                    # Environment variables template
├── pyproject.toml                  # Project dependencies
├── README.md                       # This file
└── src/                           # Source code
    ├── __init__.py
    ├── config.py                   # Configuration management
    ├── state.py                    # State definitions
    ├── llm.py                      # LLM provider management
    ├── tools.py                    # Search tools
    ├── workflow.py                 # LangGraph workflow
    └── agents/                     # Agent implementations
        ├── __init__.py
        ├── agent1_query_generator.py    # Query generation
        ├── agent2_search_executor.py    # Search execution
        ├── agent3_evaluator.py          # Result evaluation
        └── agent4_integrator.py         # Information integration
```

### Key Components

- **`src/workflow.py`**: LangGraph workflow orchestration
- **`src/agents/`**: Individual agent implementations
- **`src/state.py`**: Shared state definitions
- **`src/tools.py`**: Search and web scraping tools
- **`src/llm.py`**: LLM provider management
- **`app.py`**: Streamlit user interface

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key | Required if using OpenAI |
| `ANTHROPIC_API_KEY` | Anthropic API key | Required if using Anthropic |
| `DEFAULT_LLM_PROVIDER` | Default LLM provider | `openai` |
| `MAX_SEARCH_RESULTS` | Maximum results per search | `10` |
| `SEARCH_TIMEOUT` | Search timeout in seconds | `30` |
| `DEBUG` | Enable debug logging | `False` |

### Multiple Usage Options

1. **Web Interface (Recommended)**:
   ```bash
   streamlit run app.py
   ```

2. **Command Line Interface**:
   ```bash
   # Interactive mode
   python cli.py

   # Single query
   python cli.py -q "What is machine learning?"

   # With output file
   python cli.py -q "AI trends" -o results.json
   ```

3. **Simple Runner**:
   ```bash
   python run.py          # Run tests then start web app
   python run.py --test   # Run tests only
   ```

## 🧪 Testing

### Run System Tests

```bash
python test_system.py
```

### Test Individual Components

```python
from src.workflow import workflow

result = workflow.run(
    user_query="What is machine learning?",
    max_rounds=2
)

print(result['final_result'].summary)
```

## 🔧 Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure your API keys are correctly set in the `.env` file
2. **Search Failures**: Check internet connection and search timeout settings
3. **Memory Issues**: Reduce `MAX_SEARCH_RESULTS` for large queries
4. **Slow Performance**: Consider using faster models or reducing search rounds

### Logging

Enable debug logging by setting `DEBUG=True` in your `.env` file to see detailed execution logs.

## 🚀 Quick Start

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd langgraph-augment-2
   uv sync  # or pip install -e .
   ```

2. **Configure API keys**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Test the system**:
   ```bash
   python test_system.py
   ```

4. **Start using**:
   ```bash
   # Web interface
   streamlit run app.py

   # Or command line
   python cli.py -q "Your question here"
   ```

## 🎯 Features in Detail

### Multi-Agent Workflow

1. **Agent 1 - Query Generator**:
   - Analyzes user intent and context
   - Generates 3-5 diverse search queries
   - Creates result template structure
   - Assigns priority levels to queries

2. **Agent 2 - Search Executor**:
   - Executes searches using DuckDuckGo
   - Handles multiple query types
   - Deduplicates and ranks results
   - Optionally enhances with page content

3. **Agent 3 - Result Evaluator**:
   - Assesses result quality and completeness
   - Identifies missing information aspects
   - Decides if additional searches are needed
   - Generates feedback for improvement

4. **Agent 4 - Information Integrator**:
   - Synthesizes all search results
   - Creates comprehensive summaries
   - Extracts key points and insights
   - Provides confidence scoring

### Advanced Features

- **Adaptive Search Rounds**: System automatically performs additional searches if initial results are insufficient
- **Quality Scoring**: AI-powered evaluation of result relevance and completeness
- **Source Diversity**: Ensures results come from multiple perspectives and sources
- **Template-Based Results**: Structured output tailored to query type
- **Progress Tracking**: Real-time updates on which agent is currently active

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you encounter any issues or have questions, please open an issue on GitHub.

---

**Built with ❤️ using LangGraph and Streamlit**
```
"""Agent 2: Search Executor - Performs multi-round search operations."""

import logging
from typing import List, Dict, Any
from langgraph.types import Command

from ..state import MultiAgentState, SearchResult, SearchQuery
from ..tools import search_tools

logger = logging.getLogger(__name__)


class SearchExecutorAgent:
    """Agent responsible for executing search operations."""
    
    def __init__(self):
        self.name = "search_executor"
    
    def execute_searches(self, queries: List[SearchQuery]) -> List[SearchResult]:
        """Execute search queries and return results."""
        all_results = []
        
        # Sort queries by priority (highest first)
        sorted_queries = sorted(queries, key=lambda q: q.priority, reverse=True)
        
        for query in sorted_queries:
            logger.info(f"Executing search for: {query.query}")
            
            try:
                # Perform search using Tavily
                results = search_tools.tavily_search(query.query)

                # Tavily already provides relevance scores, but we can enhance them
                for result in results:
                    # Combine Tavi<PERSON>'s score with our query-specific scoring
                    tavily_score = result.relevance_score
                    query_score = self._calculate_relevance_score(result, query)
                    # Weighted combination: 70% Tavily score, 30% query-specific score
                    result.relevance_score = tavily_score * 0.7 + query_score * 0.3

                all_results.extend(results)

            except Exception as e:
                logger.error(f"Search failed for query '{query.query}': {e}")
                continue
        
        # Remove duplicates and sort by relevance
        unique_results = self._deduplicate_results(all_results)
        sorted_results = sorted(unique_results, key=lambda r: r.relevance_score, reverse=True)
        
        return sorted_results
    
    def _calculate_relevance_score(self, result: SearchResult, query: SearchQuery) -> float:
        """Calculate relevance score for a search result."""
        # Simple relevance scoring based on query terms in title and snippet
        query_terms = query.query.lower().split()
        title_lower = result.title.lower()
        snippet_lower = result.snippet.lower()
        
        title_matches = sum(1 for term in query_terms if term in title_lower)
        snippet_matches = sum(1 for term in query_terms if term in snippet_lower)
        
        # Weight title matches more heavily
        score = (title_matches * 0.7 + snippet_matches * 0.3) / len(query_terms)
        
        # Apply query priority as a multiplier
        score *= (query.priority / 5.0)
        
        return min(score, 1.0)  # Cap at 1.0
    
    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """Remove duplicate results based on URL."""
        seen_urls = set()
        unique_results = []
        
        for result in results:
            if result.url not in seen_urls:
                seen_urls.add(result.url)
                unique_results.append(result)
        
        return unique_results
    
    def enhance_results_with_content(self, results: List[SearchResult]) -> List[SearchResult]:
        """Enhance search results with additional content from pages."""
        return search_tools.enhance_search_results(results)

    def get_comprehensive_search_context(self, queries: List[SearchQuery]) -> Dict[str, Any]:
        """Get comprehensive search context using Tavily's advanced features."""
        try:
            # Combine all queries into a comprehensive search
            combined_query = " ".join([q.query for q in queries[:3]])  # Use top 3 queries

            # Get search context from Tavily
            context = search_tools.get_search_context(combined_query)

            return context

        except Exception as e:
            logger.error(f"Failed to get comprehensive search context: {e}")
            return {}


def agent2_node(state: MultiAgentState) -> Command:
    """Agent 2 node function for LangGraph."""
    
    agent = SearchExecutorAgent()
    
    # Get search queries from state
    queries = state.get("search_queries", [])
    
    if not queries:
        # No queries to execute, skip to evaluation
        return Command(
            goto="agent3_evaluator",
            update={
                **state,
                "search_results": [],
                "current_agent": "evaluator",
                "error_message": "No search queries provided"
            }
        )
    
    try:
        # Execute searches using Tavily
        logger.info(f"Executing {len(queries)} search queries with Tavily")
        results = agent.execute_searches(queries)

        # Get comprehensive context for better understanding
        search_context = agent.get_comprehensive_search_context(queries)

        # Enhance results with additional content if needed
        if len(results) <= 5:  # Only enhance if we have few results
            results = agent.enhance_results_with_content(results)

        # Update state with search context
        updated_state = {
            **state,
            "search_results": results,
            "search_context": search_context,  # Add Tavily context
            "current_agent": "evaluator",
            "total_sources_found": len(results)
        }

        logger.info(f"Found {len(results)} search results using Tavily")

        return Command(
            goto="agent3_evaluator",
            update=updated_state
        )
        
    except Exception as e:
        logger.error(f"Search execution failed: {e}")
        
        return Command(
            goto="agent3_evaluator",
            update={
                **state,
                "search_results": [],
                "current_agent": "evaluator",
                "error_message": f"Search execution failed: {str(e)}"
            }
        )

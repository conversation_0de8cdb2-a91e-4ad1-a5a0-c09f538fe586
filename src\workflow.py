"""LangGraph workflow for the multi-agent search system."""

import logging
from datetime import datetime
from typing import Dict, Any
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver

from .state import MultiAgentState
from .agents.agent1_query_generator import agent1_node
from .agents.agent2_search_executor import agent2_node
from .agents.agent3_evaluator import agent3_node
from .agents.agent4_integrator import agent4_node

logger = logging.getLogger(__name__)


class MultiAgentSearchWorkflow:
    """Multi-agent search workflow using LangGraph."""
    
    def __init__(self):
        self.graph = None
        self.checkpointer = MemorySaver()
        self._build_graph()
    
    def _build_graph(self):
        """Build the LangGraph workflow."""
        
        # Create the state graph
        workflow = StateGraph(MultiAgentState)
        
        # Add nodes for each agent
        workflow.add_node("agent1_query_generator", agent1_node)
        workflow.add_node("agent2_search", agent2_node)
        workflow.add_node("agent3_evaluator", agent3_node)
        workflow.add_node("agent4_integrator", agent4_node)
        
        # Define the workflow edges
        workflow.add_edge(START, "agent1_query_generator")
        
        # Agent 1 always goes to Agent 2
        # (This is handled by the Command in agent1_node)
        
        # Agent 2 always goes to Agent 3
        # (This is handled by the Command in agent2_node)
        
        # Agent 3 decides whether to go back to Agent 2 or proceed to Agent 4
        # (This is handled by the Command in agent3_node)
        
        # Agent 4 goes to END
        # (This is handled by the Command in agent4_node)
        
        # Compile the graph
        self.graph = workflow.compile(checkpointer=self.checkpointer)
        
        logger.info("Multi-agent search workflow compiled successfully")
    
    def run(self, user_query: str, user_intent: str = None, max_rounds: int = 3) -> Dict[str, Any]:
        """Run the multi-agent search workflow."""
        
        if user_intent is None:
            user_intent = f"Find comprehensive information about: {user_query}"
        
        # Initialize state
        initial_state = {
            "user_query": user_query,
            "user_intent": user_intent,
            "search_queries": [],
            "result_template": "",
            "search_results": [],
            "search_round": 1,
            "evaluation": None,
            "needs_more_search": False,
            "final_result": None,
            "current_agent": "query_generator",
            "max_rounds": max_rounds,
            "error_message": None,
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "total_sources_found": 0
        }
        
        try:
            logger.info(f"Starting multi-agent search for query: {user_query}")
            
            # Run the workflow
            config = {"configurable": {"thread_id": "search_session"}}
            final_state = self.graph.invoke(initial_state, config)
            
            logger.info("Multi-agent search workflow completed successfully")
            return final_state
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            
            # Return error state
            error_state = {
                **initial_state,
                "error_message": str(e),
                "end_time": datetime.now().isoformat(),
                "current_agent": "error"
            }
            return error_state
    
    def stream_run(self, user_query: str, user_intent: str = None, max_rounds: int = 3):
        """Run the workflow with streaming updates."""
        
        if user_intent is None:
            user_intent = f"Find comprehensive information about: {user_query}"
        
        # Initialize state
        initial_state = {
            "user_query": user_query,
            "user_intent": user_intent,
            "search_queries": [],
            "result_template": "",
            "search_results": [],
            "search_round": 1,
            "evaluation": None,
            "needs_more_search": False,
            "final_result": None,
            "current_agent": "query_generator",
            "max_rounds": max_rounds,
            "error_message": None,
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "total_sources_found": 0
        }
        
        try:
            logger.info(f"Starting streaming multi-agent search for query: {user_query}")
            
            # Stream the workflow
            config = {"configurable": {"thread_id": "search_session"}}
            
            for chunk in self.graph.stream(initial_state, config):
                yield chunk
                
        except Exception as e:
            logger.error(f"Streaming workflow execution failed: {e}")
            
            # Yield error state
            error_state = {
                **initial_state,
                "error_message": str(e),
                "end_time": datetime.now().isoformat(),
                "current_agent": "error"
            }
            yield {"error": error_state}
    
    def get_graph_visualization(self) -> bytes:
        """Get a visual representation of the workflow graph."""
        try:
            return self.graph.get_graph().draw_mermaid_png()
        except Exception as e:
            logger.error(f"Failed to generate graph visualization: {e}")
            return None


# Global workflow instance
workflow = MultiAgentSearchWorkflow()

"""Simple runner script for the multi-agent search system."""

import sys
import os
import subprocess
import argparse
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import streamlit
        import langgraph
        import langchain
        return True
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("💡 Please run: uv sync  or  pip install -e .")
        return False

def check_env_file():
    """Check if .env file exists and has required keys."""
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found!")
        print("💡 Please copy .env.example to .env and configure your API keys")
        return False
    
    # Read .env file and check for API keys
    env_content = env_file.read_text()
    has_openai = "OPENAI_API_KEY=" in env_content and "your_openai_api_key_here" not in env_content
    has_anthropic = "ANTHROPIC_API_KEY=" in env_content and "your_anthropic_api_key_here" not in env_content
    
    if not (has_openai or has_anthropic):
        print("⚠️ No API keys configured in .env file!")
        print("💡 Please add at least one API key (OPENAI_API_KEY or ANTHROPIC_API_KEY)")
        return False
    
    return True

def run_tests():
    """Run system tests."""
    print("🧪 Running system tests...")
    try:
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def run_streamlit():
    """Run the Streamlit application."""
    print("🚀 Starting Streamlit application...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Failed to start Streamlit: {e}")

def main():
    parser = argparse.ArgumentParser(description="Multi-Agent Search System Runner")
    parser.add_argument("--test", action="store_true", help="Run tests only")
    parser.add_argument("--skip-checks", action="store_true", help="Skip dependency and environment checks")
    
    args = parser.parse_args()
    
    print("🔍 Multi-Agent Search & Information Integration System")
    print("=" * 60)
    
    if not args.skip_checks:
        # Check dependencies
        if not check_dependencies():
            return 1
        
        # Check environment
        if not check_env_file():
            return 1
    
    if args.test:
        # Run tests only
        success = run_tests()
        return 0 if success else 1
    else:
        # Run tests first, then start app
        print("🔧 Running pre-flight checks...")
        if run_tests():
            print("✅ All tests passed!")
            print()
            run_streamlit()
        else:
            print("❌ Tests failed. Please fix issues before running the app.")
            return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

"""Simple runner script for the multi-agent search system."""

import sys
import os
import subprocess
import argparse
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import streamlit
        import langgraph
        import langchain
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("💡 请运行: uv sync  或  pip install -e .")
        return False

def check_env_file():
    """Check if .env file exists and has required keys."""
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ 未找到.env文件!")
        print("💡 请将.env.example复制为.env并配置您的API密钥")
        return False

    # Read .env file and check for API keys
    env_content = env_file.read_text()
    has_openai = "OPENAI_API_KEY=" in env_content and "your_openai_api_key_here" not in env_content and "your_modelscope_api_key_here" not in env_content
    has_anthropic = "ANTHROPIC_API_KEY=" in env_content and "your_anthropic_api_key_here" not in env_content
    has_modelscope = "MODEL_PROVIDER=ModelScope" in env_content and has_openai

    if not (has_openai or has_anthropic or has_modelscope):
        print("⚠️ .env文件中未配置API密钥!")
        print("💡 请添加至少一个API密钥 (ModelScope/OpenAI使用OPENAI_API_KEY 或 ANTHROPIC_API_KEY)")
        return False

    return True

def run_tests():
    """Run system tests."""
    print("🧪 运行系统测试...")
    try:
        result = subprocess.run([sys.executable, "test_system.py"],
                              capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("错误:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        return False

def run_streamlit():
    """Run the Streamlit application."""
    print("🚀 启动Streamlit应用...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"])
    except KeyboardInterrupt:
        print("\n👋 用户停止了应用")
    except Exception as e:
        print(f"❌ 启动Streamlit失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="多智能体搜索系统启动器")
    parser.add_argument("--test", action="store_true", help="仅运行测试")
    parser.add_argument("--skip-checks", action="store_true", help="跳过依赖和环境检查")

    args = parser.parse_args()

    print("🔍 多智能体搜索与信息整合系统")
    print("=" * 60)

    if not args.skip_checks:
        # Check dependencies
        if not check_dependencies():
            return 1

        # Check environment
        if not check_env_file():
            return 1

    if args.test:
        # Run tests only
        success = run_tests()
        return 0 if success else 1
    else:
        # Run tests first, then start app
        print("🔧 运行预检查...")
        if run_tests():
            print("✅ 所有测试通过!")
            print()
            run_streamlit()
        else:
            print("❌ 测试失败。请修复问题后再运行应用。")
            return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())

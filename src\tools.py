"""Search tools for the multi-agent system."""

import requests
from typing import List, Dict, Any
from tavily import <PERSON>ly<PERSON><PERSON>
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import time
import logging

from .config import Config
from .state import SearchResult

logger = logging.getLogger(__name__)


class SearchTools:
    """Collection of search tools."""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        # Initialize Tavily client
        if Config.TAVILY_API_KEY:
            self.tavily_client = TavilyClient(api_key=Config.TAVILY_API_KEY)
        else:
            self.tavily_client = None
            logger.warning("Tavily API key not configured")

    def tavily_search(self, query: str, max_results: int = None) -> List[SearchResult]:
        """Perform Tavily search."""
        if max_results is None:
            max_results = Config.MAX_SEARCH_RESULTS

        if not self.tavily_client:
            logger.error("Tavily client not initialized - API key missing")
            return []

        try:
            # Perform search using Tavily
            response = self.tavily_client.search(
                query=query,
                max_results=max_results,
                search_depth="advanced",  # Use advanced search for better results
                include_answer=False,     # We'll generate our own answer
                include_raw_content=True  # Get full content for better analysis
            )

            results = []
            search_results = response.get('results', [])

            for result in search_results:
                search_result = SearchResult(
                    title=result.get('title', ''),
                    url=result.get('url', ''),
                    snippet=result.get('content', ''),
                    relevance_score=result.get('score', 0.5)  # Tavily provides relevance scores
                )
                results.append(search_result)

            logger.info(f"Found {len(results)} results for query: {query}")
            return results

        except Exception as e:
            logger.error(f"Tavily search failed: {e}")
            return []
    
    def get_page_content(self, url: str, max_chars: int = 5000) -> str:
        """Extract content from a webpage."""
        try:
            response = self.session.get(url, timeout=Config.SEARCH_TIMEOUT)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # Truncate if too long
            if len(text) > max_chars:
                text = text[:max_chars] + "..."
                
            return text
            
        except Exception as e:
            logger.error(f"Failed to get content from {url}: {e}")
            return ""
    
    def enhance_search_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """Enhance search results with additional content."""
        enhanced_results = []
        
        for result in results:
            try:
                # Get additional content from the page
                content = self.get_page_content(result.url, max_chars=1000)
                if content:
                    # Enhance the snippet with page content
                    enhanced_snippet = f"{result.snippet}\n\nAdditional content: {content[:500]}..."
                    result.snippet = enhanced_snippet
                
                enhanced_results.append(result)
                
                # Add small delay to be respectful
                time.sleep(0.5)
                
            except Exception as e:
                logger.warning(f"Failed to enhance result {result.url}: {e}")
                enhanced_results.append(result)
        
        return enhanced_results
    
    def search_multiple_queries(self, queries: List[str]) -> List[SearchResult]:
        """Search multiple queries and combine results."""
        all_results = []
        seen_urls = set()

        for query in queries:
            results = self.tavily_search(query)

            # Deduplicate by URL
            for result in results:
                if result.url not in seen_urls:
                    seen_urls.add(result.url)
                    all_results.append(result)

        # Sort by relevance score (Tavily provides scores)
        all_results.sort(key=lambda r: r.relevance_score, reverse=True)
        return all_results[:Config.MAX_SEARCH_RESULTS]

    def get_search_context(self, query: str, max_results: int = None) -> Dict[str, Any]:
        """Get comprehensive search context using Tavily's context feature."""
        if max_results is None:
            max_results = Config.MAX_SEARCH_RESULTS

        if not self.tavily_client:
            logger.error("Tavily client not initialized - API key missing")
            return {}

        try:
            # Use Tavily's context search for comprehensive results
            response = self.tavily_client.get_search_context(
                query=query,
                max_results=max_results,
                search_depth="advanced"
            )

            return response

        except Exception as e:
            logger.error(f"Tavily context search failed: {e}")
            return {}


# Global search tools instance
search_tools = SearchTools()

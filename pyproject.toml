[project]
name = "langgraph-augment-2"
version = "0.1.0"
description = "Multi-agent search and information integration system using LangGraph and Streamlit"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "langgraph>=0.2.74",
    "langchain>=0.3.0",
    "langchain-openai>=0.2.0",
    "langchain-anthropic>=0.2.0",
    "streamlit>=1.48.0",
    "python-dotenv>=1.0.0",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "duckduckgo-search>=6.0.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.8.0"
]

# 🚀 快速启动指南

## 📋 前提条件

- Python 3.11 或更高版本
- ModelScope API 密钥
- Tavily 搜索 API 密钥

## ⚡ 5分钟快速启动

### 1. 获取API密钥

#### ModelScope API密钥
1. 访问 [ModelScope](https://www.modelscope.cn/)
2. 注册并登录账户
3. 获取API密钥

#### Tavily 搜索API密钥
1. 访问 [Tavily](https://tavily.com/)
2. 注册账户并获取API密钥
3. Tavily提供强大的AI搜索功能

### 2. 配置环境

```bash
# 1. 安装依赖
uv sync  # 或 pip install -e .

# 2. 配置API密钥
# .env 文件已经预配置，只需要替换API密钥：
```

编辑 `.env` 文件，配置您的API密钥：

```env
# ModelScope API configuration
OPENAI_API_KEY=您的ModelScope_API密钥
MODEL_BASE_URL=https://api-inference.modelscope.cn/v1/
MODEL_NAME=Qwen/Qwen3-Coder-480B-A35B-Instruct
MODEL_PROVIDER=ModelScope
DEFAULT_LLM_PROVIDER=modelscope

# Tavily Search API configuration
TAVILY_API_KEY=您的Tavily_API密钥
```

### 3. 测试配置

```bash
# 测试ModelScope配置
python test_modelscope.py

# 运行完整系统测试
python test_system.py
```

### 4. 启动应用

```bash
# 方式1: Web界面 (推荐)
streamlit run app.py

# 方式2: 命令行界面
python cli.py

# 方式3: 一键启动 (包含测试)
python run.py
```

## 🎯 第一次使用

### Web界面使用

1. 打开浏览器访问 `http://localhost:8501`
2. 在搜索框输入您的问题，例如：
   - "什么是机器学习？"
   - "Python编程的最佳实践"
   - "人工智能的最新发展"
3. 点击"🚀 Search"按钮
4. 观察四个智能体的工作过程
5. 查看综合搜索结果

### 命令行使用

```bash
# 交互模式
python cli.py

# 单次查询
python cli.py -q "什么是深度学习？"

# 保存结果到文件
python cli.py -q "AI发展趋势" -o results.json
```

## 🔧 常见问题

### Q: API密钥错误
**A**: 确保在 `.env` 文件中正确设置了ModelScope API密钥

### Q: 搜索失败
**A**: 检查网络连接，确保可以访问搜索引擎

### Q: 响应慢
**A**: ModelScope模型较大，首次调用可能需要更多时间

### Q: 中文支持
**A**: Qwen模型对中文有很好的支持，可以直接使用中文提问

## 🎉 成功标志

如果看到以下输出，说明系统运行正常：

```
✅ All tests passed!
🎉 ModelScope configuration is working correctly!
💡 You can now run the full system with: streamlit run app.py
```

## 📚 更多功能

- **多轮搜索**: 系统会自动进行多轮搜索以获得更完整的结果
- **质量评估**: AI会评估搜索结果的质量和完整性
- **智能整合**: 将多个来源的信息整合成连贯的答案
- **搜索历史**: Web界面会保存您的搜索历史

## 🆘 需要帮助？

- 查看 `README.md` 了解详细文档
- 运行 `python test_system.py` 进行系统诊断
- 检查 `SUMMARY.md` 了解项目架构

---

**开始您的智能搜索之旅！** 🚀

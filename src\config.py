"""Configuration module for the multi-agent search system."""

import os
from typing import Literal
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Application configuration."""

    # API Keys
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")

    # ModelScope Configuration
    MODEL_BASE_URL = os.getenv("MODEL_BASE_URL", "https://api-inference.modelscope.cn/v1/")
    MODEL_NAME = os.getenv("MODEL_NAME", "Qwen/Qwen3-Coder-480B-A35B-Instruct")
    MODEL_PROVIDER = os.getenv("MODEL_PROVIDER", "ModelScope")

    # LLM Configuration
    DEFAULT_LLM_PROVIDER: Literal["openai", "anthropic", "modelscope"] = os.getenv("DEFAULT_LLM_PROVIDER", "modelscope")
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    ANTHROPIC_MODEL = os.getenv("ANTHROPIC_MODEL", "claude-3-5-sonnet-20241022")

    # Tavily Search Configuration
    TAVILY_API_KEY = os.getenv("TAVILY_API_KEY")

    # Search Configuration
    MAX_SEARCH_RESULTS = int(os.getenv("MAX_SEARCH_RESULTS", "10"))
    SEARCH_TIMEOUT = int(os.getenv("SEARCH_TIMEOUT", "30"))
    
    # Application Settings
    DEBUG = os.getenv("DEBUG", "False").lower() == "true"
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    
    @classmethod
    def validate(cls) -> bool:
        """Validate configuration."""
        if cls.DEFAULT_LLM_PROVIDER == "openai" and not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY is required when using OpenAI provider")
        if cls.DEFAULT_LLM_PROVIDER == "anthropic" and not cls.ANTHROPIC_API_KEY:
            raise ValueError("ANTHROPIC_API_KEY is required when using Anthropic provider")
        if cls.DEFAULT_LLM_PROVIDER == "modelscope":
            if not cls.OPENAI_API_KEY:
                raise ValueError("OPENAI_API_KEY is required when using ModelScope provider (used as API key)")
            if not cls.MODEL_BASE_URL:
                raise ValueError("MODEL_BASE_URL is required when using ModelScope provider")
            if not cls.MODEL_NAME:
                raise ValueError("MODEL_NAME is required when using ModelScope provider")

        # Validate Tavily API key
        if not cls.TAVILY_API_KEY:
            raise ValueError("TAVILY_API_KEY is required for search functionality")

        return True

# Validate configuration on import
Config.validate()

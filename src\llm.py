"""LLM utilities for the multi-agent system."""

from typing import Optional, Dict, Any
from langchain_openai import ChatOpenA<PERSON>
from langchain_anthropic import Cha<PERSON><PERSON><PERSON>hropic
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.language_models import BaseChatModel

from .config import Config


class LLMManager:
    """Manages LLM instances and provides unified interface."""
    
    def __init__(self):
        self._models: Dict[str, BaseChatModel] = {}
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize available LLM models."""
        # Initialize OpenAI model if API key is available
        if Config.OPENAI_API_KEY and Config.DEFAULT_LLM_PROVIDER == "openai":
            self._models["openai"] = ChatOpenAI(
                model=Config.OPENAI_MODEL,
                api_key=Config.OPENAI_API_KEY,
                temperature=0.1
            )

        # Initialize Anthropic model if API key is available
        if Config.ANTHROPIC_API_KEY:
            self._models["anthropic"] = ChatAnthropic(
                model=Config.ANTHROPIC_MODEL,
                api_key=Config.ANTHROPIC_API_KEY,
                temperature=0.1
            )

        # Initialize ModelScope model (using OpenAI-compatible API)
        if Config.DEFAULT_LLM_PROVIDER == "modelscope" and Config.OPENAI_API_KEY:
            self._models["modelscope"] = ChatOpenAI(
                model=Config.MODEL_NAME,
                api_key=Config.OPENAI_API_KEY,
                base_url=Config.MODEL_BASE_URL,
                temperature=0.1
            )
    
    def get_model(self, provider: Optional[str] = None) -> BaseChatModel:
        """Get LLM model instance."""
        if provider is None:
            provider = Config.DEFAULT_LLM_PROVIDER
        
        if provider not in self._models:
            raise ValueError(f"Model provider '{provider}' not available. Available: {list(self._models.keys())}")
        
        return self._models[provider]
    
    def invoke(self, messages: list, provider: Optional[str] = None) -> str:
        """Invoke LLM with messages."""
        model = self.get_model(provider)
        response = model.invoke(messages)
        return response.content
    
    def create_messages(self, system_prompt: str, user_prompt: str) -> list:
        """Create message list for LLM."""
        return [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]


# Global LLM manager instance
llm_manager = LLMManager()

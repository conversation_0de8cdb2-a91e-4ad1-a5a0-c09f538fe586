"""Streamlit application for the multi-agent search system."""

import streamlit as st
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.workflow import workflow
from src.config import Config

# Configure logging
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="多智能体搜索系统",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

def initialize_session_state():
    """Initialize Streamlit session state."""
    if "search_history" not in st.session_state:
        st.session_state.search_history = []
    if "current_search" not in st.session_state:
        st.session_state.current_search = None
    if "search_in_progress" not in st.session_state:
        st.session_state.search_in_progress = False

def display_header():
    """Display the application header."""
    st.title("🔍 多智能体搜索与信息整合系统")
    st.markdown("""
    本系统使用四个专门的AI智能体来提供全面的搜索结果：
    - **智能体1**: 生成有针对性的搜索查询和结果模板
    - **智能体2**: 执行多轮网络搜索
    - **智能体3**: 评估结果质量并决定是否需要更多搜索
    - **智能体4**: 整合所有发现并生成综合答案
    """)

def display_sidebar():
    """Display the sidebar with configuration options."""
    with st.sidebar:
        st.header("⚙️ 配置选项")

        # LLM Provider selection
        provider_options = []
        if Config.DEFAULT_LLM_PROVIDER == "modelscope" and Config.OPENAI_API_KEY:
            provider_options.append("modelscope")
        if Config.OPENAI_API_KEY and Config.DEFAULT_LLM_PROVIDER == "openai":
            provider_options.append("openai")
        if Config.ANTHROPIC_API_KEY:
            provider_options.append("anthropic")

        if provider_options:
            selected_provider = st.selectbox(
                "LLM 提供商",
                provider_options,
                index=provider_options.index(Config.DEFAULT_LLM_PROVIDER) if Config.DEFAULT_LLM_PROVIDER in provider_options else 0
            )
        else:
            st.error("未配置API密钥！请检查您的.env文件。")
            selected_provider = None

        # Search configuration
        max_rounds = st.slider("最大搜索轮数", 1, 5, 3)
        
        # Display current configuration
        st.subheader("📊 当前设置")
        st.write(f"**LLM提供商**: {selected_provider}")
        if selected_provider == "modelscope":
            st.write(f"**模型**: {Config.MODEL_NAME}")
            st.write(f"**API地址**: {Config.MODEL_BASE_URL}")
        st.write(f"**搜索引擎**: Tavily")
        st.write(f"**搜索API**: {'✅ 已配置' if Config.TAVILY_API_KEY else '❌ 未配置'}")
        st.write(f"**最大结果数**: {Config.MAX_SEARCH_RESULTS}")
        st.write(f"**超时时间**: {Config.SEARCH_TIMEOUT}秒")
        st.write(f"**最大轮数**: {max_rounds}")

        return selected_provider, max_rounds

def display_search_interface():
    """Display the main search interface."""
    st.header("🔎 搜索查询")

    # Search input
    col1, col2 = st.columns([3, 1])

    with col1:
        user_query = st.text_input(
            "输入您的搜索查询：",
            placeholder="例如：人工智能的最新发展是什么？",
            key="search_query"
        )

    with col2:
        search_button = st.button(
            "🚀 搜索",
            type="primary",
            disabled=st.session_state.search_in_progress
        )

    # Advanced options
    with st.expander("🔧 高级选项"):
        user_intent = st.text_area(
            "指定您的意图（可选）：",
            placeholder="例如：我需要为研究论文准备一个全面的概述",
            height=100
        )

    return user_query, user_intent, search_button

def display_search_progress(search_state: Dict[str, Any]):
    """Display search progress and current agent status."""

    # Progress indicators
    agents = ["查询生成器", "搜索执行器", "结果评估器", "信息整合器"]
    current_agent = search_state.get("current_agent", "")

    # Create progress columns
    cols = st.columns(4)

    agent_mapping = {
        "query_generator": 0,
        "search_executor": 1,
        "evaluator": 2,
        "integrator": 3
    }

    current_index = agent_mapping.get(current_agent, -1)

    for i, (col, agent_name) in enumerate(zip(cols, agents)):
        with col:
            if i == current_index:
                st.success(f"🔄 {agent_name}")
            elif i < current_index:
                st.success(f"✅ {agent_name}")
            else:
                st.info(f"⏳ {agent_name}")

    # Current status
    status_mapping = {
        "query_generator": "查询生成中",
        "search_executor": "搜索执行中",
        "evaluator": "结果评估中",
        "integrator": "信息整合中",
        "completed": "已完成"
    }
    status_text = status_mapping.get(current_agent, current_agent.replace('_', ' ').title())
    st.info(f"**当前状态**: {status_text}")

    # Search round info
    if search_state.get("search_round", 0) > 1:
        st.warning(f"**搜索轮次**: {search_state.get('search_round', 1)} / {search_state.get('max_rounds', 3)}")

def display_search_results(final_state: Dict[str, Any]):
    """Display the final search results."""

    if final_state.get("error_message"):
        st.error(f"❌ 搜索失败: {final_state['error_message']}")
        return

    final_result = final_state.get("final_result")
    if not final_result:
        st.warning("⚠️ 未生成结果")
        return

    # Main results
    st.header("📋 搜索结果")

    # Summary
    st.subheader("📝 摘要")
    st.write(final_result.summary)

    # Key points
    if final_result.key_points:
        st.subheader("🔑 关键要点")
        for point in final_result.key_points:
            st.write(f"• {point}")

    # Sources
    if final_result.sources:
        st.subheader("📚 信息来源")
        for i, source in enumerate(final_result.sources, 1):
            st.write(f"{i}. [{source}]({source})")

    # Confidence and metadata
    col1, col2, col3 = st.columns(3)

    with col1:
        confidence = final_result.confidence_score
        st.metric("置信度评分", f"{confidence:.2f}", delta=None)

    with col2:
        total_sources = final_state.get("total_sources_found", 0)
        st.metric("找到的来源", total_sources)

    with col3:
        search_rounds = final_state.get("search_round", 1)
        st.metric("搜索轮数", search_rounds)

def display_search_details(final_state: Dict[str, Any]):
    """Display detailed search information."""

    with st.expander("🔍 搜索详情"):

        # Search queries
        queries = final_state.get("search_queries", [])
        if queries:
            st.subheader("生成的搜索查询")
            for i, query in enumerate(queries, 1):
                st.write(f"**{i}.** {query.query} (优先级: {query.priority})")
                st.caption(query.context)

        # Evaluation results
        evaluation = final_state.get("evaluation")
        if evaluation:
            st.subheader("质量评估")
            st.write(f"**质量评分**: {evaluation.quality_score:.2f}")
            st.write(f"**是否充分**: {'✅ 是' if evaluation.is_sufficient else '❌ 否'}")
            if evaluation.missing_aspects:
                st.write("**缺失方面**:")
                for aspect in evaluation.missing_aspects:
                    st.write(f"• {aspect}")
            st.write(f"**反馈**: {evaluation.feedback}")

        # Timing information
        start_time = final_state.get("start_time")
        end_time = final_state.get("end_time")
        if start_time and end_time:
            start_dt = datetime.fromisoformat(start_time)
            end_dt = datetime.fromisoformat(end_time)
            duration = (end_dt - start_dt).total_seconds()
            st.write(f"**搜索耗时**: {duration:.2f} 秒")

def main():
    """Main application function."""
    
    # Initialize session state
    initialize_session_state()
    
    # Display header
    display_header()
    
    # Display sidebar and get configuration
    provider, max_rounds = display_sidebar()
    
    if not provider:
        st.stop()
    
    # Display search interface
    user_query, user_intent, search_button = display_search_interface()
    
    # Handle search execution
    if search_button and user_query.strip():
        st.session_state.search_in_progress = True

        # Create progress placeholder
        progress_placeholder = st.empty()
        results_placeholder = st.empty()

        try:
            # Run the search workflow with streaming
            with st.spinner("🔄 多智能体搜索进行中..."):
                final_state = workflow.run(
                    user_query=user_query.strip(),
                    user_intent=user_intent.strip() if user_intent else None,
                    max_rounds=max_rounds
                )

            # Store results
            st.session_state.current_search = final_state
            st.session_state.search_history.append({
                "query": user_query,
                "timestamp": datetime.now(),
                "results": final_state
            })

            # Display results
            with results_placeholder.container():
                display_search_results(final_state)
                display_search_details(final_state)

        except Exception as e:
            st.error(f"❌ 搜索失败: {str(e)}")
            logger.error(f"Search execution failed: {e}")

        finally:
            st.session_state.search_in_progress = False
    
    # Display search history
    if st.session_state.search_history:
        st.header("📚 搜索历史")

        for i, search in enumerate(reversed(st.session_state.search_history[-5:]), 1):
            with st.expander(f"🔍 {search['query']} - {search['timestamp'].strftime('%Y-%m-%d %H:%M')}"):
                display_search_results(search['results'])

if __name__ == "__main__":
    main()

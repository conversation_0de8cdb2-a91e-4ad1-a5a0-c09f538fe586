"""Streamlit application for the multi-agent search system."""

import streamlit as st
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.workflow import workflow
from src.config import Config

# Configure logging
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Multi-Agent Search System",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

def initialize_session_state():
    """Initialize Streamlit session state."""
    if "search_history" not in st.session_state:
        st.session_state.search_history = []
    if "current_search" not in st.session_state:
        st.session_state.current_search = None
    if "search_in_progress" not in st.session_state:
        st.session_state.search_in_progress = False

def display_header():
    """Display the application header."""
    st.title("🔍 Multi-Agent Search & Information Integration System")
    st.markdown("""
    This system uses four specialized AI agents to provide comprehensive search results:
    - **Agent 1**: Generates targeted search queries and result templates
    - **Agent 2**: Executes multi-round searches across the web
    - **Agent 3**: Evaluates result quality and determines if more searching is needed
    - **Agent 4**: Integrates all findings into a comprehensive answer
    """)

def display_sidebar():
    """Display the sidebar with configuration options."""
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # LLM Provider selection
        provider_options = []
        if Config.OPENAI_API_KEY:
            provider_options.append("openai")
        if Config.ANTHROPIC_API_KEY:
            provider_options.append("anthropic")
        
        if provider_options:
            selected_provider = st.selectbox(
                "LLM Provider",
                provider_options,
                index=provider_options.index(Config.DEFAULT_LLM_PROVIDER) if Config.DEFAULT_LLM_PROVIDER in provider_options else 0
            )
        else:
            st.error("No API keys configured! Please check your .env file.")
            selected_provider = None
        
        # Search configuration
        max_rounds = st.slider("Max Search Rounds", 1, 5, 3)
        
        # Display current configuration
        st.subheader("📊 Current Settings")
        st.write(f"**Provider**: {selected_provider}")
        st.write(f"**Max Results**: {Config.MAX_SEARCH_RESULTS}")
        st.write(f"**Timeout**: {Config.SEARCH_TIMEOUT}s")
        st.write(f"**Max Rounds**: {max_rounds}")
        
        return selected_provider, max_rounds

def display_search_interface():
    """Display the main search interface."""
    st.header("🔎 Search Query")
    
    # Search input
    col1, col2 = st.columns([3, 1])
    
    with col1:
        user_query = st.text_input(
            "Enter your search query:",
            placeholder="e.g., What are the latest developments in artificial intelligence?",
            key="search_query"
        )
    
    with col2:
        search_button = st.button(
            "🚀 Search",
            type="primary",
            disabled=st.session_state.search_in_progress
        )
    
    # Advanced options
    with st.expander("🔧 Advanced Options"):
        user_intent = st.text_area(
            "Specify your intent (optional):",
            placeholder="e.g., I need a comprehensive overview for a research paper",
            height=100
        )
    
    return user_query, user_intent, search_button

def display_search_progress(search_state: Dict[str, Any]):
    """Display search progress and current agent status."""
    
    # Progress indicators
    agents = ["Query Generator", "Search Executor", "Result Evaluator", "Information Integrator"]
    current_agent = search_state.get("current_agent", "")
    
    # Create progress columns
    cols = st.columns(4)
    
    for i, (col, agent_name) in enumerate(zip(cols, agents)):
        with col:
            if agent_name.lower().replace(" ", "_") in current_agent:
                st.success(f"🔄 {agent_name}")
            elif i < agents.index(next((a for a in agents if a.lower().replace(" ", "_") in current_agent), agents[0])):
                st.success(f"✅ {agent_name}")
            else:
                st.info(f"⏳ {agent_name}")
    
    # Current status
    st.info(f"**Current Status**: {current_agent.replace('_', ' ').title()}")
    
    # Search round info
    if search_state.get("search_round", 0) > 1:
        st.warning(f"**Search Round**: {search_state.get('search_round', 1)} / {search_state.get('max_rounds', 3)}")

def display_search_results(final_state: Dict[str, Any]):
    """Display the final search results."""
    
    if final_state.get("error_message"):
        st.error(f"❌ Search failed: {final_state['error_message']}")
        return
    
    final_result = final_state.get("final_result")
    if not final_result:
        st.warning("⚠️ No results generated")
        return
    
    # Main results
    st.header("📋 Search Results")
    
    # Summary
    st.subheader("📝 Summary")
    st.write(final_result.summary)
    
    # Key points
    if final_result.key_points:
        st.subheader("🔑 Key Points")
        for point in final_result.key_points:
            st.write(f"• {point}")
    
    # Sources
    if final_result.sources:
        st.subheader("📚 Sources")
        for i, source in enumerate(final_result.sources, 1):
            st.write(f"{i}. [{source}]({source})")
    
    # Confidence and metadata
    col1, col2, col3 = st.columns(3)
    
    with col1:
        confidence = final_result.confidence_score
        st.metric("Confidence Score", f"{confidence:.2f}", delta=None)
    
    with col2:
        total_sources = final_state.get("total_sources_found", 0)
        st.metric("Sources Found", total_sources)
    
    with col3:
        search_rounds = final_state.get("search_round", 1)
        st.metric("Search Rounds", search_rounds)

def display_search_details(final_state: Dict[str, Any]):
    """Display detailed search information."""
    
    with st.expander("🔍 Search Details"):
        
        # Search queries
        queries = final_state.get("search_queries", [])
        if queries:
            st.subheader("Generated Search Queries")
            for i, query in enumerate(queries, 1):
                st.write(f"**{i}.** {query.query} (Priority: {query.priority})")
                st.caption(query.context)
        
        # Evaluation results
        evaluation = final_state.get("evaluation")
        if evaluation:
            st.subheader("Quality Evaluation")
            st.write(f"**Quality Score**: {evaluation.quality_score:.2f}")
            st.write(f"**Sufficient**: {'✅ Yes' if evaluation.is_sufficient else '❌ No'}")
            if evaluation.missing_aspects:
                st.write("**Missing Aspects**:")
                for aspect in evaluation.missing_aspects:
                    st.write(f"• {aspect}")
            st.write(f"**Feedback**: {evaluation.feedback}")
        
        # Timing information
        start_time = final_state.get("start_time")
        end_time = final_state.get("end_time")
        if start_time and end_time:
            start_dt = datetime.fromisoformat(start_time)
            end_dt = datetime.fromisoformat(end_time)
            duration = (end_dt - start_dt).total_seconds()
            st.write(f"**Search Duration**: {duration:.2f} seconds")

def main():
    """Main application function."""
    
    # Initialize session state
    initialize_session_state()
    
    # Display header
    display_header()
    
    # Display sidebar and get configuration
    provider, max_rounds = display_sidebar()
    
    if not provider:
        st.stop()
    
    # Display search interface
    user_query, user_intent, search_button = display_search_interface()
    
    # Handle search execution
    if search_button and user_query.strip():
        st.session_state.search_in_progress = True
        
        # Create progress placeholder
        progress_placeholder = st.empty()
        results_placeholder = st.empty()
        
        try:
            # Run the search workflow with streaming
            with st.spinner("🔄 Multi-agent search in progress..."):
                final_state = workflow.run(
                    user_query=user_query.strip(),
                    user_intent=user_intent.strip() if user_intent else None,
                    max_rounds=max_rounds
                )
            
            # Store results
            st.session_state.current_search = final_state
            st.session_state.search_history.append({
                "query": user_query,
                "timestamp": datetime.now(),
                "results": final_state
            })
            
            # Display results
            with results_placeholder.container():
                display_search_results(final_state)
                display_search_details(final_state)
            
        except Exception as e:
            st.error(f"❌ Search failed: {str(e)}")
            logger.error(f"Search execution failed: {e}")
        
        finally:
            st.session_state.search_in_progress = False
    
    # Display search history
    if st.session_state.search_history:
        st.header("📚 Search History")
        
        for i, search in enumerate(reversed(st.session_state.search_history[-5:]), 1):
            with st.expander(f"🔍 {search['query']} - {search['timestamp'].strftime('%Y-%m-%d %H:%M')}"):
                display_search_results(search['results'])

if __name__ == "__main__":
    main()
